package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.ListModulesOfPublishedCourseQuery
import com.cleevio.fundedmind.domain.common.constant.CourseModuleState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class ListModulesOfPublishedCourseQueryHandlerTest @Autowired constructor(
    private val underTest: ListModulesOfPublishedCourseQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list course modules - verify mappings`() {
        // given
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })
        dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            title = "Module 1",
            description = "Description 1",
            comingSoon = false,
            entityModifier = {
                it.changeThumbnailPictureDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_DESKTOP_THUMBNAIL,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeThumbnailPictureMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_MOBILE_THUMBNAIL,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        ).also { module ->
            dataHelper.getLesson(
                id = 11.toUUID(),
                courseModuleId = module.id,
                durationInSeconds = 30,
                listingOrder = 1,
                entityModifier = { it.softDelete() },
            )
            dataHelper.getLesson(id = 12.toUUID(), courseModuleId = module.id, durationInSeconds = 10, listingOrder = 1)
            dataHelper.getLesson(id = 13.toUUID(), courseModuleId = module.id, durationInSeconds = 10, listingOrder = 2)
        }

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = student.id,
                courseId = course.id,
            ),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            moduleId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            totalDurationInSeconds shouldBe 20
            title shouldBe "Module 1"
            description shouldBe "Description 1"
            thumbnailDesktop shouldNotBe null
            thumbnailDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            thumbnailMobile shouldNotBe null
            thumbnailMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            lessonCount shouldBe 2
            isLockedForMe shouldBe false
            unlockedData shouldNotBe null
            unlockedData!!.run {
                courseModuleState shouldBe CourseModuleState.NOT_STARTED
                watchLessonId shouldBe 12.toUUID()
                finishedLessons shouldBe 0
            }
        }
    }

    @Test
    fun `should list course modules with correct state`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }
        val user2 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }
        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // module with in-progress state
        dataHelper.getCourseModule(courseId = course.id, listingOrder = 1).also { module1 ->
            dataHelper.getLesson(id = 111.toUUID(), courseModuleId = module1.id, listingOrder = 1).also { lesson ->
                // this lesson is finished by user1
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = lesson.id,
                    entityModifier = { it.finish() },
                )
                dataHelper.getLessonProgress(
                    userId = user2.id,
                    lessonId = lesson.id,
                    entityModifier = { it.finish() },
                )
            }
            dataHelper.getLesson(id = 112.toUUID(), courseModuleId = module1.id, listingOrder = 2).also { lesson ->
                // this lesson is being watched by user1, but it is not the last watched lesson
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = lesson.id,
                    seconds = 20,
                    updatedTimestamp = "2025-01-01T10:00:00Z".toInstant(), // 01.01. 10:00
                )
            }
            dataHelper.getLesson(id = 113.toUUID(), courseModuleId = module1.id, listingOrder = 3).also { lesson ->
                // this lesson is the last watched lesson by user1
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = lesson.id,
                    seconds = 10,
                    updatedTimestamp = "2025-01-02T10:00:00Z".toInstant(), // 02.01. 10:00
                )
            }
        }

        // module with finished state
        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        dataHelper.getCourseModule(courseId = course.id, listingOrder = 2).also { module2 ->
            dataHelper.getLesson(id = 211.toUUID(), courseModuleId = module2.id, listingOrder = 1).also { lesson ->
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = lesson.id,
                    entityModifier = { it.finish(finishedAt) },
                )
            }

            dataHelper.getLesson(id = 212.toUUID(), courseModuleId = module2.id, listingOrder = 2).also { lesson ->
                // this lesson is finished by user1
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = lesson.id,
                    entityModifier = { it.finish(finishedAt) }, // 01.01. 10:00
                    // user has finished lesson but is re-watching it day later
                    updatedTimestamp = finishedAt.plus(1, ChronoUnit.DAYS), // 02.01. 10:00
                )

                // this lesson is in progress for different user
                dataHelper.getLessonProgress(
                    userId = user2.id,
                    lessonId = lesson.id,
                    seconds = 20,
                )
            }

            dataHelper.getCourseModuleProgress(
                userId = user1.id,
                courseModuleId = module2.id,
                finishedAt = finishedAt, // 01.01. 10:00
            )
        }

        // module with coming soon state
        dataHelper.getCourseModule(courseId = course.id, listingOrder = 3, comingSoon = true).also { module3 ->
            dataHelper.getLesson(id = 311.toUUID(), courseModuleId = module3.id, listingOrder = 1)
            dataHelper.getLesson(id = 312.toUUID(), courseModuleId = module3.id, listingOrder = 2)
        }

        // module with not-started state
        dataHelper.getCourseModule(courseId = course.id, listingOrder = 4).also { module4 ->
            dataHelper.getLesson(
                id = 411.toUUID(),
                courseModuleId = module4.id,
                listingOrder = 1,
                entityModifier = { it.softDelete() },
            )
            // first non-deleted lesson
            dataHelper.getLesson(
                id = 412.toUUID(),
                courseModuleId = module4.id,
                listingOrder = 1,
            )
        }

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = user1.id,
                courseId = course.id,
            ),
        )

        // then
        result.data shouldHaveSize 4
        result.data.run {
            this[0].run {
                unlockedData shouldNotBe null
                unlockedData!!.run {
                    courseModuleState shouldBe CourseModuleState.IN_PROGRESS
                    watchLessonId shouldBe 113.toUUID()
                    finishedLessons shouldBe 1
                }
            }
            this[1].run {
                unlockedData shouldNotBe null
                unlockedData!!.run {
                    courseModuleState shouldBe CourseModuleState.FINISHED
                    watchLessonId shouldBe 211.toUUID()
                    finishedLessons shouldBe 2
                }
            }
            this[2].run {
                unlockedData shouldNotBe null
                unlockedData!!.run {
                    courseModuleState shouldBe CourseModuleState.COMING_SOON
                    watchLessonId shouldBe null
                    finishedLessons shouldBe 0
                }
            }
            this[3].run {
                unlockedData shouldNotBe null
                unlockedData!!.run {
                    courseModuleState shouldBe CourseModuleState.NOT_STARTED
                    watchLessonId shouldBe 412.toUUID()
                    finishedLessons shouldBe 0
                }
            }
        }
    }

    @Test
    fun `should list course module with IN_PROGRESS state when it has finished lesson and second was not started`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS)
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            entityModifier = { it.createPicturesAndPublish() },
        )

        // module has 2 lessons, 1 was finished and 2 was not started - module should be in progress
        dataHelper.getCourseModule(courseId = course.id, listingOrder = 1).also { module1 ->
            // this lesson is finished
            dataHelper.getLesson(id = 111.toUUID(), courseModuleId = module1.id, listingOrder = 1).also { lesson ->
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = lesson.id,
                    entityModifier = { it.finish() },
                )
            }
            // this lesson is not started
            dataHelper.getLesson(id = 112.toUUID(), courseModuleId = module1.id, listingOrder = 2)
        }

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = user1.id,
                courseId = course.id,
            ),
        )

        // then
        result.data shouldHaveSize 1
        result.data.run {
            this[0].run {
                unlockedData shouldNotBe null
                unlockedData!!.run {
                    courseModuleState shouldBe CourseModuleState.IN_PROGRESS
                    watchLessonId shouldBe 112.toUUID()
                    finishedLessons shouldBe 1
                }
            }
        }
    }

    @Test
    fun `should list course modules only from given published course`() {
        // given
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS)
        dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)

        val publishedCourse = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val unpublishedCourse = dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
        )

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = publishedCourse.id)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = unpublishedCourse.id)

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = student.id,
                courseId = publishedCourse.id,
            ),
        )

        // then
        result.data.map { it.moduleId } shouldBe listOf(1.toUUID())
    }

    @Test
    fun `should list course modules ordered by listing order`() {
        // given
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS)
        dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id, listingOrder = 3)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id, listingOrder = 1)
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = course.id, listingOrder = 2)

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = student.id,
                courseId = course.id,
            ),
        )

        // then
        result.data.map { it.moduleId } shouldBe listOf(2.toUUID(), 3.toUUID(), 1.toUUID())
    }

    @Test
    fun `should list course modules of published course without deleted modules`() {
        // given
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS)
        dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        dataHelper.getCourseModule(
            id = 2.toUUID(),
            courseId = course.id,
            entityModifier = { it.softDelete() },
        )

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = student.id,
                courseId = course.id,
            ),
        )

        // then
        result.data.map { it.moduleId } shouldBe listOf(1.toUUID())
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = ["ADMIN", "TRADER"],
        mode = EnumSource.Mode.INCLUDE,
    )
    fun `should list course modules - admin and trader have access to all courses`(userRole: UserRole) {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = userRole)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = user.id,
                courseId = course.id,
            ),
        )

        // then
        result.data.single().run {
            isLockedForMe shouldBe false
            unlockedData shouldNotBe null
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = StudentTier::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["MASTERCLASS", "EXCLUSIVE"],
    )
    fun `should list course modules - should lock based on student tier`(studentTier: StudentTier) {
        // given
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = studentTier)
        dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            visibleToTiers = listOf(StudentTier.BASECAMP),
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        // when
        val result = underTest.handle(
            ListModulesOfPublishedCourseQuery(
                userId = student.id,
                courseId = course.id,
            ),
        )

        // then
        result.data.single().run {
            isLockedForMe shouldBe true
            unlockedData shouldBe null
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(
            fileId = dataHelper.getImage(
                type = FileType.COURSE_DESKTOP_INTRO_PHOTO,
            ).id,
        )
        changeIntroPictureMobile(
            fileId = dataHelper.getImage(
                type = FileType.COURSE_MOBILE_INTRO_PHOTO,
            ).id,
        )
        publish()
    }
}
