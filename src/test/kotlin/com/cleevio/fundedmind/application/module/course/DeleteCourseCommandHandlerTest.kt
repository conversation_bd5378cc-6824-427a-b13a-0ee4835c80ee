package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.DeleteCourseCommand
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.coursemodule.CourseModuleRepository
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DeleteCourseCommandHandlerTest @Autowired constructor(
    private val underTest: DeleteCourseCommandHandler,
    private val courseRepository: CourseRepository,
    private val courseModuleRepository: CourseModuleRepository,
    private val lessonRepository: LessonRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete course`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        underTest.handle(
            DeleteCourseCommand(courseId = 1.toUUID()),
        )

        courseRepository.findByIdOrNull(1.toUUID())!!.isDeleted shouldBe true
    }

    @Test
    fun `should soft delete course with its modules and all lessons`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        // create modules for the course
        val module1 = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val module2 = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)

        // create lessons for modules
        dataHelper.getLesson(id = 11.toUUID(), courseModuleId = module1.id)
        dataHelper.getLesson(id = 21.toUUID(), courseModuleId = module2.id)
        dataHelper.getLesson(id = 22.toUUID(), courseModuleId = module2.id)

        // when
        underTest.handle(
            DeleteCourseCommand(courseId = course.id),
        )

        // then
        courseRepository.findByIdOrNull(course.id)!!.isDeleted shouldBe true

        courseModuleRepository.findAll().run {
            first { it.id == module1.id }.isDeleted shouldBe true
            first { it.id == module2.id }.isDeleted shouldBe true
        }

        lessonRepository.findAll().run {
            first { it.id == 11.toUUID() }.isDeleted shouldBe true
            first { it.id == 21.toUUID() }.isDeleted shouldBe true
            first { it.id == 22.toUUID() }.isDeleted shouldBe true
        }
    }
}
