package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.command.CreateNewLessonCommand
import com.cleevio.fundedmind.application.module.lesson.command.LessonAttachmentInput
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFoundException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotRelatedToCourseException
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentRepository
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class CreateNewLessonCommandHandlerTest @Autowired constructor(
    private val underTest: CreateNewLessonCommandHandler,
    private val lessonRepository: LessonRepository,
    private val lessonAttachmentRepository: LessonAttachmentRepository,
) : IntegrationTest() {

    @Test
    fun `should create new lesson with attachments`() {
        // given
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id).id,
        )

        // when
        val result = underTest.handle(
            defaultCommand(
                courseModuleId = courseModule.id,
                attachments = listOf(
                    LessonAttachmentInput(
                        lessonAttachmentId = null,
                        name = "attachment1",
                        type = LessonAttachmentType.PDF,
                    ),
                    LessonAttachmentInput(
                        lessonAttachmentId = null,
                        name = "attachment2",
                        type = LessonAttachmentType.XLSX,
                    ),
                ),
            ),
        )

        // then
        lessonRepository.findByIdOrNull(result.id)!!.run {
            courseModuleId shouldBe courseModule.id
            listingOrder shouldBe 1
            title shouldBe "Lesson 1"
            description shouldBe "Description"
            videoUrl shouldBe "video-url"
            durationInSeconds shouldBe 3600
            thumbnailUrl shouldBe "thumbnail-url"
            thumbnailAnimationUrl shouldBe "thumbnail-animation-url"
        }

        val attachments = lessonAttachmentRepository.findAll()
        attachments shouldHaveSize 2
        attachments.first { it.nameWithExtension == "attachment1" }.run {
            displayOrder shouldBe 1
            type shouldBe LessonAttachmentType.PDF
            lessonId shouldBe result.id
            attachmentDocumentFileId shouldBe null
        }
        attachments.first { it.nameWithExtension == "attachment2" }.run {
            displayOrder shouldBe 2
            type shouldBe LessonAttachmentType.XLSX
            lessonId shouldBe result.id
            attachmentDocumentFileId shouldBe null
        }
    }

    @Test
    fun `should throw if course module does not exist`() {
        shouldThrow<CourseModuleNotFoundException> {
            underTest.handle(
                defaultCommand(courseModuleId = 999.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if course module is not related to course`() {
        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id).id,
        )

        shouldThrow<CourseModuleNotRelatedToCourseException> {
            underTest.handle(
                defaultCommand(
                    courseId = 999.toUUID(),
                    courseModuleId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should create with next lesson listing order`() {
        // given
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id).id,
        )

        dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 1,
        )
        dataHelper.getLesson(
            id = 2.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 2,
            entityModifier = { it.softDelete() },
        )

        // when
        val result = underTest.handle(
            defaultCommand(courseModuleId = courseModule.id),
        )

        // then
        lessonRepository.findByIdOrNull(result.id)!!.listingOrder shouldBe 2 // highest non-deleted + 1
    }

    private fun defaultCommand(
        courseId: UUID = 1.toUUID(),
        courseModuleId: UUID,
        title: String = "Lesson 1",
        description: String = "Description",
        videoUrl: String = "video-url",
        durationInSeconds: Int = 3600,
        thumbnailUrl: String = "thumbnail-url",
        thumbnailAnimationUrl: String = "thumbnail-animation-url",
        attachments: List<LessonAttachmentInput> = emptyList(),
    ) = CreateNewLessonCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        title = title,
        description = description,
        videoUrl = videoUrl,
        durationInSeconds = durationInSeconds,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        attachments = attachments,
    )
}
