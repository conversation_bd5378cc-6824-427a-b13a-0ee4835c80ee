package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.rest.request.DeleteProductCommand
import com.cleevio.fundedmind.application.module.product.exception.ProductIsSaleableException
import com.cleevio.fundedmind.application.module.product.exception.ProductRelatedToMentoringMeetingInFutureException
import com.cleevio.fundedmind.application.module.product.exception.ProductRelatedToUnfinishedMentoringException
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.domain.product.ProductRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.temporal.ChronoUnit

class DeleteProductCommandHandlerTest @Autowired constructor(
    private val underTest: DeleteProductCommandHandler,
    private val productRepository: ProductRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete product when it has no restrictions`() {
        // given
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            entityModifier = {
                it.makeUnsaleable()
            },
        )

        // when
        underTest.handle(
            DeleteProductCommand(
                productId = 1.toUUID(),
            ),
        )

        // then
        productRepository.findByIdOrNull(1.toUUID())!!.isDeleted shouldBe true
    }

    @Test
    fun `should throw if deleting saleable product`() {
        // given
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            entityModifier = { it.makeSaleable() },
        )

        // when & then
        shouldThrow<ProductIsSaleableException> {
            underTest.handle(
                DeleteProductCommand(
                    productId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if product has unfinished mentorings`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            entityModifier = { it.makeUnsaleable() },
        )

        val student = dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring with sessions left (only 2 out of 5 sessions used)
        dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 2,
        )

        // when & then
        shouldThrow<ProductRelatedToUnfinishedMentoringException> {
            underTest.handle(
                DeleteProductCommand(
                    productId = product.id,
                ),
            )
        }
    }

    @Test
    fun `should delete product when it has mentorings but all are finished`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            entityModifier = { it.makeUnsaleable() },
        )

        val student = dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring with no sessions left (all 5 sessions used)
        dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5,
        )

        // when
        underTest.handle(
            DeleteProductCommand(
                productId = product.id,
            ),
        )

        // then
        productRepository.findByIdOrNull(product.id)!!.isDeleted shouldBe true
    }

    @Test
    fun `should throw if product has mentoring meetings to attend in future`() {
        // given
        val now = "2025-04-14T10:00:00Z".toInstant()
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            entityModifier = { it.makeUnsaleable() },
        )

        val student = dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring
        val mentoring = dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5,
        )

        // Create a future mentoring meeting (starts in 1 hour, ends in 2 hours)
        dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.plus(1, ChronoUnit.HOURS),
            finishAt = now.plus(2, ChronoUnit.HOURS),
        )

        // when & then - Verify exception is thrown when product has future mentoring meetings
        shouldThrow<ProductRelatedToMentoringMeetingInFutureException> {
            underTest.handle(
                DeleteProductCommand(
                    productId = product.id,
                    now = now,
                ),
            )
        }
    }

    @Test
    fun `should throw if product has ongoing mentoring meetings`() {
        // given
        val now = "2025-04-14T10:00:00Z".toInstant()
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            entityModifier = { it.makeUnsaleable() },
        )

        val student = dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring
        val mentoring = dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5,
        )

        // Create an ongoing mentoring meeting (started 1 hour ago, ends in 1 hour)
        dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.minus(1, ChronoUnit.HOURS),
            finishAt = now.plus(1, ChronoUnit.HOURS),
        )

        // when & then - Verify exception is thrown when product has ongoing mentoring meetings
        shouldThrow<ProductRelatedToMentoringMeetingInFutureException> {
            underTest.handle(
                DeleteProductCommand(
                    productId = product.id,
                    now = now,
                ),
            )
        }
    }

    @Test
    fun `should delete product when it has mentoring meetings but all are in the past`() {
        // given
        val now = "2025-04-14T10:00:00Z".toInstant()
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            entityModifier = { it.makeUnsaleable() },
        )

        val student = dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring with all sessions used
        val mentoring = dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5,
        )

        // Create a past mentoring meeting (ended 1 hour ago)
        dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.minus(2, ChronoUnit.HOURS),
            finishAt = now.minus(1, ChronoUnit.HOURS),
        )

        // when - Delete the product
        underTest.handle(
            DeleteProductCommand(
                productId = product.id,
                now = now,
            ),
        )

        // then - Verify the product is marked as deleted
        productRepository.findByIdOrNull(product.id)!!.isDeleted shouldBe true
    }

    @Test
    fun `should delete product when it has future mentoring meetings but all are modified`() {
        // given
        val now = "2025-04-14T10:00:00Z".toInstant()
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            entityModifier = { it.makeUnsaleable() },
        )

        val student = dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring
        val mentoring = dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5,
        )

        // Create a future mentoring meeting that is modified (cancelled or rescheduled)
        dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.plus(1, ChronoUnit.HOURS),
            finishAt = now.plus(2, ChronoUnit.HOURS),
            entityModifier = { meeting ->
                // Mark the meeting as modified
                meeting.cancel(
                    reason = "Test cancellation",
                    initiatorType = InitiatorType.MENTOR,
                )
            },
        )

        // when - Delete the product
        underTest.handle(
            DeleteProductCommand(
                productId = product.id,
                now = now,
            ),
        )

        // then - Verify the product is marked as deleted
        productRepository.findByIdOrNull(product.id)!!.isDeleted shouldBe true
    }
}
