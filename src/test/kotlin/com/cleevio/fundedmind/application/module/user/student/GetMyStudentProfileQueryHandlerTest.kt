package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.student.exception.StudentNotFoundException
import com.cleevio.fundedmind.application.module.user.student.query.GetMyStudentProfileQuery
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant

class GetMyStudentProfileQueryHandlerTest @Autowired constructor(
    private val underTest: GetMyStudentProfileQueryHandler,
) : IntegrationTest() {
    @Test
    fun `should get my student profile - verify mappings`() {
        dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            country = Country.CZ,
            studentTier = StudentTier.MASTERCLASS,
            tierUpgradedAt = "2025-01-01T07:00:00Z".toInstant(),
            phone = "+420 123 456 789",
            biography = "I am a student",
            gameLevel = GameLevel.TWO,
            locationId = dataHelper.getUserLocation(
                id = 1.toUUID(),
                street = "123 Main St",
                city = "Prague",
                postalCode = "12345",
                state = "Czech Republic",
                latitude = 50.0755,
                longitude = 14.4378,
                obfuscatedLatitude = 50.0,
                obfuscatedLongitude = 14.4,
            ).id,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.activateDiscordSubscription(expiresAt = "2025-01-01T10:00:00Z".toInstant())
            },
        )
        dataHelper.getStudentDiscord(
            studentId = 1.toUUID(),
            userName = "discord-username",
        )

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            studentId shouldBe 1.toUUID()
            studentTier shouldBe StudentTier.MASTERCLASS
            tierUpgradedAt shouldBe "2025-01-01T07:00:00Z".toInstant()
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            phone shouldBe "+420 123 456 789"
            biography shouldBe "I am a student"
            country shouldBe Country.CZ
            gameLevel shouldBe GameLevel.TWO
            profilePicture!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "123"
            }
            discordSubscription shouldBe true
            discordSubscriptionExpiresAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            discordUsername shouldBe "discord-username"
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            levelVisibility shouldBe LevelVisibility.ENABLED
            location shouldNotBe null
            location!!.run {
                street shouldBe "123 Main St"
                city shouldBe "Prague"
                postalCode shouldBe "12345"
                state shouldBe "Czech Republic"
                location.latitude shouldBe 50.0755
                location.longitude shouldBe 14.4378
                obfuscatedLocation.latitude shouldBe 50.0
                obfuscatedLocation.longitude shouldBe 14.4
            }
        }
    }

    @Test
    fun `should get my student profile - no profile picture`() {
        dataHelper.getStudent(
            id = 1.toUUID(),
        )

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            studentId shouldBe 1.toUUID()
            profilePicture shouldBe null
        }
    }

    @Test
    fun `should throw if student not found`() {
        shouldThrow<StudentNotFoundException> {
            underTest.handle(
                GetMyStudentProfileQuery(
                    studentId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should return result when student exists but has not StudentDiscord`() {
        val student = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
        )

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            studentId shouldBe 1.toUUID()
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            discordSubscription shouldBe false
            discordSubscriptionExpiresAt shouldBe null
            discordUsername shouldBe null
            location shouldBe null
        }
    }

    @Test
    fun `should return result when student exists but has only deleted StudentDiscord`() {
        val student = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            entityModifier = {
                it.activateDiscordSubscription(expiresAt = "2025-01-01T10:00:00Z".toInstant())
                it.deactivateDiscordSubscription()
            },
        )

        // Create a StudentDiscord and then soft-delete it
        dataHelper.getStudentDiscord(
            studentId = student.id,
            userName = "deleted-discord-username",
            entityModifier = { it.softDelete() },
        )

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            studentId shouldBe 1.toUUID()
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            discordSubscription shouldBe false
            discordSubscriptionExpiresAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            discordUsername shouldBe null
            location shouldBe null
        }
    }

    @Test
    fun `should return result when student exists but has one deleted student discord and one active `() {
        val student = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            entityModifier = {
                it.activateDiscordSubscription(expiresAt = "2025-01-01T10:00:00Z".toInstant())
                it.deactivateDiscordSubscription()
                it.activateDiscordSubscription(expiresAt = "2025-01-02T10:00:00Z".toInstant())
            },
        )

        // Create a StudentDiscord and then soft-delete it
        dataHelper.getStudentDiscord(
            studentId = student.id,
            userName = "deleted-discord-username",
            entityModifier = { it.softDelete() },
        )

        // Create a StudentDiscord and then soft-delete it
        dataHelper.getStudentDiscord(
            studentId = student.id,
            userName = "discord-username",
        )

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            studentId shouldBe 1.toUUID()
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            discordSubscription shouldBe true
            discordSubscriptionExpiresAt shouldBe "2025-01-02T10:00:00Z".toInstant()
            discordUsername shouldBe "discord-username"
            location shouldBe null
        }
    }

    @Test
    fun `should get my student profile with location with null address fields`() {
        dataHelper.getStudent(
            id = 1.toUUID(),
            locationId = dataHelper.getUserLocation(
                id = 1.toUUID(),
                street = null,
                city = null,
                postalCode = null,
                state = null,
            ).id,
        )

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            location shouldNotBe null
            location!!.run {
                street shouldBe null
                city shouldBe null
                postalCode shouldBe null
                state shouldBe null
            }
        }
    }

    @Test
    fun `should get my student profile in onboarding`() {
        dataHelper.getStudentForOnboarding(id = 1.toUUID())

        val result = underTest.handle(
            GetMyStudentProfileQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            studentId shouldBe 1.toUUID()
            studentTier shouldBe StudentTier.NO_TIER
            tierUpgradedAt shouldBe Instant.EPOCH
            firstName shouldBe "uživatel"
            lastName shouldBe "platformy"
            phone shouldBe null
            biography shouldBe null
            country shouldBe null
            gameLevel shouldBe GameLevel.ZERO
            profilePicture shouldBe null
            discordSubscription shouldBe false
            discordSubscriptionExpiresAt shouldBe null
            discordUsername shouldBe null
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            levelVisibility shouldBe LevelVisibility.ENABLED
            location shouldBe null
        }
    }
}
