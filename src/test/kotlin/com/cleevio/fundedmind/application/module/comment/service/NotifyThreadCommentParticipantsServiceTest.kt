package com.cleevio.fundedmind.application.module.comment.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.domain.comment.ThreadCommentNotificationRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class NotifyThreadCommentParticipantsServiceTest @Autowired constructor(
    private val underTest: NotifyThreadCommentParticipantsService,
    private val threadCommentNotificationRepository: ThreadCommentNotificationRepository,
) : IntegrationTest() {

    @Test
    fun `should not notify when comment has no threadId`() {
        // given
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "John", lastName = "Doe")
        }

        val comment = dataHelper.getLessonComment(
            id = 1.toUUID(),
            appUserId = user.id,
            lessonId = dataHelper.getLesson(
                courseModuleId = dataHelper.getCourseModule(
                    courseId = dataHelper.getCourse(
                        traderId = dataHelper.getTrader().id,
                    ).id,
                ).id,
            ).id,
            threadId = null, // Main comment without thread
        )

        // when
        underTest.notifyParticipants(comment.id, Instant.now())

        // then
        // No notifications should be created
        threadCommentNotificationRepository.findAll().size shouldBe 0
    }

    @Test
    fun `should notify parent comment author when new comment is added to thread`() {
        // given
        // Parent comment author
        val parentAuthor = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Parent", lastName = "Author")
        }

        // New comment author
        val newCommentAuthor = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "New", lastName = "Author")
        }

        val lesson = dataHelper.getLesson(
            courseModuleId = dataHelper.getCourseModule(
                courseId = dataHelper.getCourse(
                    traderId = dataHelper.getTrader().id,
                ).id,
            ).id,
        )

        // Create parent comment
        val parentComment = dataHelper.getLessonComment(
            id = 10.toUUID(),
            appUserId = parentAuthor.id,
            lessonId = lesson.id,
            threadId = null, // Parent comment has no threadId
            text = "Parent comment",
        )

        // Create new comment in thread
        val newComment = dataHelper.getLessonComment(
            id = 11.toUUID(),
            appUserId = newCommentAuthor.id,
            lessonId = lesson.id,
            threadId = parentComment.id, // Reference to parent comment
            text = "Reply to parent",
        )

        every { sendEmailService.sendEmailNewThreadComment(any()) } just Runs

        // when
        underTest.notifyParticipants(newComment.id, Instant.now())

        // then
        // Verify notification was created for parent author
        val notification = threadCommentNotificationRepository.findAll().run {
            this.size shouldBe 1
            single()!!.apply {
                this.threadId shouldBe newComment.threadId
                this.threadId shouldBe parentComment.id
                this.appUserId shouldBe parentAuthor.id
            }
        }

        verify {
            sendEmailService.sendEmailNewThreadComment(threadCommentNotificationId = notification.id)
        }
    }

    @Test
    fun `should respect rate limiting when multiple comments are added to thread`() {
        // given
        // Parent comment author
        val now = Instant.now()

        val parentAuthor = dataHelper.getAppUser(id = 20.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Parent", lastName = "Author")
        }

        // Second comment author
        val secondCommentAuthor = dataHelper.getAppUser(id = 21.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Second", lastName = "Author")
        }

        // Third comment author
        val thirdCommentAuthor = dataHelper.getAppUser(id = 22.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Third", lastName = "Author")
        }

        val lesson = dataHelper.getLesson(
            courseModuleId = dataHelper.getCourseModule(
                courseId = dataHelper.getCourse(
                    traderId = dataHelper.getTrader().id,
                ).id,
            ).id,
        )

        // Create parent comment
        val parentComment = dataHelper.getLessonComment(
            id = 1.toUUID(),
            appUserId = parentAuthor.id,
            lessonId = lesson.id,
            threadId = null, // Parent comment has no threadId
            text = "Parent comment",
        )

        // Create second comment in thread
        val secondComment = dataHelper.getLessonComment(
            id = 2.toUUID(),
            appUserId = secondCommentAuthor.id,
            lessonId = lesson.id,
            threadId = parentComment.id, // Reference to parent comment
            text = "Second comment in thread",
        ).also {
            dataHelper.getThreadCommentNotification(
                id = 21.toUUID(),
                appUserId = parentAuthor.id,
                threadId = parentComment.id,
                lastNotifiedAt = now.minus(1, ChronoUnit.MINUTES),
            )
        }

        // Create third comment in thread (created after second comment but within rate limit window)
        val thirdComment = dataHelper.getLessonComment(
            id = 3.toUUID(),
            appUserId = thirdCommentAuthor.id,
            lessonId = lesson.id,
            threadId = parentComment.id, // Reference to parent comment
            text = "Third comment in thread",
        )

        every { sendEmailService.sendEmailNewThreadComment(any()) } just Runs

        // when
        underTest.notifyParticipants(commentId = thirdComment.id, now = now)

        // then
        // Verify notifications
        val notifications = threadCommentNotificationRepository.findAll().apply {
            size shouldBe 2
        }

        notifications.first { it.appUserId == parentAuthor.id }.apply {
            id shouldBe 21.toUUID()
            appUserId shouldBe parentAuthor.id
            threadId shouldBe parentComment.id
            lastNotifiedAt shouldBeAbout now.minus(1, ChronoUnit.MINUTES)
        }

        val newNotification = notifications.first { it.appUserId == secondCommentAuthor.id }.apply {
            appUserId shouldBe secondCommentAuthor.id
            threadId shouldBe parentComment.id
            lastNotifiedAt shouldBeAbout now
        }

        // Verify event was published once for the second comment author
        verify {
            sendEmailService.sendEmailNewThreadComment(threadCommentNotificationId = newNotification.id)
        }
    }

    @Test
    fun `should not notify user when they reply to their own comment`() {
        // given
        val now = Instant.now()

        // User who creates both the parent comment and the reply
        val user = dataHelper.getAppUser(id = 30.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Self", lastName = "Replier")
        }

        val lesson = dataHelper.getLesson(
            courseModuleId = dataHelper.getCourseModule(
                courseId = dataHelper.getCourse(
                    traderId = dataHelper.getTrader().id,
                ).id,
            ).id,
        )

        // Create parent comment by the user
        val parentComment = dataHelper.getLessonComment(
            id = 30.toUUID(),
            appUserId = user.id,
            lessonId = lesson.id,
            threadId = null, // Parent comment has no threadId
            text = "Parent comment by user",
        )

        // Create reply by the same user
        val replyComment = dataHelper.getLessonComment(
            id = 31.toUUID(),
            appUserId = user.id, // Same user as parent comment
            lessonId = lesson.id,
            threadId = parentComment.id, // Reference to parent comment
            text = "Reply to my own comment",
        )

        every { sendEmailService.sendEmailNewThreadComment(any()) } just Runs

        // when
        underTest.notifyParticipants(commentId = replyComment.id, now = now)

        // then
        // Verify no notifications were created (user shouldn't be notified of their own reply)
        threadCommentNotificationRepository.findAll().size shouldBe 0

        // Verify no email was sent
        verify(exactly = 0) {
            sendEmailService.sendEmailNewThreadComment(any())
        }
    }
}
