package com.cleevio.fundedmind.application.module.gamelevelprogress.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamelevelprogress.GameLevelProgressRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class GameLevelTwoProcessingServiceTest @Autowired constructor(
    private val underTest: PostStrategyCourseLevelTwoRewardService,
    private val gameLevelProgressRepository: GameLevelProgressRepository,
    private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should create new game level progress for level two when there is an approved backtesting document`() {
        // Given
        val student = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        // Create a completed strategy course
        createFinishedStrategyCourse(studentId = student.id)

        // Create an approved backtesting document
        dataHelper.getGameDocument(
            id = 5.toUUID(),
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.ONE,
            entityModifier = { it.approveAwaiting() },
        )

        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs

        // When
        underTest.processAfterFinishingStrategyCourse(studentId = student.id)

        // Then
        val progresses = gameLevelProgressRepository.findAllByStudentId(student.id)
        progresses shouldHaveSize 1
        progresses.single().run {
            studentId shouldBe 1.toUUID()
            gameLevel shouldBe GameLevel.TWO
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.TWO) }
    }

    @Test
    fun `should recompute current and reached level for game document when there is a waiting backtesting document`() {
        // Given
        val student = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        // Create a completed strategy course
        createFinishedStrategyCourse(studentId = student.id)

        // Create a WAITING backtesting document
        val gameDocument = dataHelper.getGameDocument(
            id = 5.toUUID(),
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.ONE, // Initially same level
        )

        // When
        underTest.processAfterFinishingStrategyCourse(studentId = student.id)

        // Then
        // Verify no game level progress was created
        gameLevelProgressRepository.findAllByStudentId(student.id) shouldHaveSize 0

        // Verify the game document was recomputed
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.TWO // Should be updated to level TWO
            state shouldBe GameDocumentApprovalState.WAITING
            approvedAt shouldBe null
        }
    }

    @Test
    fun `should do nothing when there is a denied backtesting document`() {
        // Given
        val student = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        // Create a completed strategy course
        createFinishedStrategyCourse(studentId = student.id)

        // Create a denied backtesting document
        dataHelper.getGameDocument(
            id = 5.toUUID(),
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.ONE,
            entityModifier = { it.denyAwaiting("Denied for testing") },
        )

        // When
        underTest.processAfterFinishingStrategyCourse(studentId = student.id)

        // Then
        // Verify no game level progress was created
        gameLevelProgressRepository.findAllByStudentId(student.id) shouldHaveSize 0

        // Verify the game document is still denied without change to levels
        gameDocumentRepository.findByIdOrNull(5.toUUID())!!.run {
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.ONE
            state shouldBe GameDocumentApprovalState.DENIED
        }
    }

    @Test
    fun `should do nothing if student already has game level progress for level two`() {
        // Given
        val student = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        // Create a completed strategy course
        createFinishedStrategyCourse(studentId = student.id)

        // Create an approved backtesting document
        dataHelper.getGameDocument(
            id = 5.toUUID(),
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
            entityModifier = { it.approveAwaiting() },
        )

        // Create existing game level progress for level TWO
        dataHelper.getGameLevelProgress(
            id = 6.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.TWO,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
        )

        // When
        underTest.processAfterFinishingStrategyCourse(studentId = student.id)

        // Then
        // Verify only one game level progress exists (the one we created)
        gameLevelProgressRepository.findAllByStudentId(student.id)
            .map { it.id } shouldBe listOf(6.toUUID())
    }

    private fun createFinishedStrategyCourse(studentId: UUID) {
        val course = dataHelper.getCourse(
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            courseCategory = CourseCategory.STRATEGY,
        )
        dataHelper.getCourseProgress(
            userId = studentId,
            courseId = course.id,
            finishedAt = "2024-01-01T10:00:00Z".toInstant(),
        )
    }
}
