package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.DataTestHelper
import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.onboarding.command.SaveOnboardQuestionnaireCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentAlreadyOnboardedException
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class SaveOnboardQuestionnaireCommandHandlerTest @Autowired constructor(
    private val underTest: SaveOnboardQuestionnaireCommandHandler,
    private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `should save questionnaire`() {
        dataHelper.getStudentForOnboarding(1.toUUID())

        underTest.handle(
            SaveOnboardQuestionnaireCommand(
                studentId = 1.toUUID(),
                questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput(),
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.questionnaire shouldNotBe null
    }

    @Test
    fun `should throw if student already onboarded`() {
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = { it.changeState(OnboardingState.ONBOARDING_COMPLETE) },
        )

        shouldThrow<StudentAlreadyOnboardedException> {
            underTest.handle(
                SaveOnboardQuestionnaireCommand(
                    studentId = 1.toUUID(),
                    questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput(),
                ),
            )
        }
    }
}
