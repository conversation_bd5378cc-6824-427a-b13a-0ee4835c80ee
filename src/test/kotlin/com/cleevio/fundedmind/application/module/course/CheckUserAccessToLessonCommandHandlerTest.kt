package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.CheckUserAccessToLessonCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.exception.CourseIsLockedForUserException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class CheckUserAccessToLessonCommandHandlerTest @Autowired constructor(
    private val underTest: CheckUserAccessToLessonCommandHandler,
) : IntegrationTest() {

    @Test
    fun `should throw if lesson is not accessible to user`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
        )
        val courseModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id)

        // when/then
        shouldThrow<CourseIsLockedForUserException> {
            underTest.handle(
                CheckUserAccessToLessonCommand(
                    userId = user.id,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonId = lesson.id,
                ),
            )
        }
    }

    @Test
    fun `should not throw if lesson is accessible to user`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = true,
        )
        val courseModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id)

        // when/then
        shouldNotThrow<CourseIsLockedForUserException> {
            underTest.handle(
                CheckUserAccessToLessonCommand(
                    userId = user.id,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonId = lesson.id,
                ),
            )
        }
    }
}
