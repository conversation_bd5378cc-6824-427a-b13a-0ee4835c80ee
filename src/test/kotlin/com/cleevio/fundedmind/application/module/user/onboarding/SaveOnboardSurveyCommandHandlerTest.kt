package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.module.user.onboarding.command.SaveOnboardSurveyCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentAlreadyOnboardedException
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.location.UserLocationRepository
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class SaveOnboardSurveyCommandHandlerTest @Autowired constructor(
    private val underTest: SaveOnboardSurveyCommandHandler,
    private val studentRepository: StudentRepository,
    private val userLocationRepository: UserLocationRepository,
) : IntegrationTest() {

    @Test
    fun `should save survey`() {
        dataHelper.getStudentForOnboarding(1.toUUID())

        // Create a location input
        val locationInput = UserLocationInput(
            street = "Test Street 123",
            city = "Test City",
            postalCode = "12345",
            state = "Test Country",
            geoLocation = GeoLocationInput(
                latitude = 49.1950610,
                longitude = 16.606836,
            ),
        )

        underTest.handle(
            SaveOnboardSurveyCommand(
                studentId = 1.toUUID(),
                firstName = "Karol",
                lastName = "Duchon",
                phone = "+************",
                biography = "Slovenske ESO",
                country = Country.SK,
                firstNameVocative = "Karle",
                lastNameVocative = "Duchoni",
                location = locationInput,
            ),
        )

        // Verify onboarding data
        val student = studentRepository.findByIdOrNull(1.toUUID())!!
        student.run {
            firstName shouldBe "Karol"
            lastName shouldBe "Duchon"
            phone shouldBe "+************"
            biography shouldBe "Slovenske ESO"
            country shouldBe Country.SK
            firstNameVocative shouldBe "Karle"
            lastNameVocative shouldBe "Duchoni"
            questionnaire shouldBe null
            locationId shouldNotBe null
        }

        // Verify location data
        userLocationRepository.findByIdOrNull(student.locationId!!)!!.run {
            street shouldBe "Test Street 123"
            city shouldBe "Test City"
            postalCode shouldBe "12345"
            state shouldBe "Test Country"
            latitude shouldBe 49.1950610
            longitude shouldBe 16.606836

            // Verify that obfuscated values are different from original values
            obfuscatedLatitude shouldNotBe latitude
            obfuscatedLongitude shouldNotBe longitude
        }
    }

    @Test
    fun `should throw if student already onboarded`() {
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = { it.changeState(OnboardingState.ONBOARDING_COMPLETE) },
        )

        // Create a location input
        val locationInput = UserLocationInput(
            street = "Test Street 123",
            city = "Test City",
            postalCode = "12345",
            state = "Test Country",
            geoLocation = GeoLocationInput(
                latitude = 49.1950610,
                longitude = 16.606836,
            ),
        )

        shouldThrow<StudentAlreadyOnboardedException> {
            underTest.handle(
                SaveOnboardSurveyCommand(
                    studentId = 1.toUUID(),
                    firstName = "Karol",
                    lastName = "Duchon",
                    phone = "+************",
                    biography = "Slovenske ESO",
                    country = Country.SK,
                    firstNameVocative = "Karle",
                    lastNameVocative = "Duchoni",
                    location = locationInput,
                ),
            )
        }
    }
}
