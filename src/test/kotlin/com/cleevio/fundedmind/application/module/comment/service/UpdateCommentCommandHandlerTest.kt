package com.cleevio.fundedmind.application.module.comment.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.comment.command.UpdateCommentCommand
import com.cleevio.fundedmind.domain.comment.CommentRepository
import com.cleevio.fundedmind.domain.comment.exception.CommentOwnerIsWrongException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class UpdateCommentCommandHandlerTest @Autowired constructor(
    private val underTest: UpdateCommentCommandHandler,
    private val commentRepository: CommentRepository,
) : IntegrationTest() {

    @Test
    fun `should update existing comment when user is owner`() {
        // given
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        val lesson = dataHelper.getLesson(
            courseModuleId = dataHelper.getCourseModule(
                courseId = dataHelper.getCourse(
                    traderId = dataHelper.getTrader().id,
                ).id,
            ).id,
        )
        val comment = dataHelper.getLessonComment(
            appUserId = user.id,
            lessonId = lesson.id,
            text = "Original text",
        )

        // when
        underTest.handle(
            UpdateCommentCommand(
                appUserId = user.id,
                commentId = comment.id,
                text = "Updated text",
            ),
        )

        // then
        commentRepository.findByIdOrNull(comment.id)!!.text shouldBe "Updated text"
    }

    @Test
    fun `should throw exception when user is not the owner of the comment`() {
        // given
        val owner = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        val otherUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        val lesson = dataHelper.getLesson(
            courseModuleId = dataHelper.getCourseModule(
                courseId = dataHelper.getCourse(
                    traderId = dataHelper.getTrader().id,
                ).id,
            ).id,
        )
        val comment = dataHelper.getLessonComment(
            appUserId = owner.id,
            lessonId = lesson.id,
            text = "Original text",
        )

        // when / then
        shouldThrow<CommentOwnerIsWrongException> {
            underTest.handle(
                UpdateCommentCommand(
                    appUserId = otherUser.id,
                    commentId = comment.id,
                    text = "Updated text",
                ),
            )
        }
    }
}
