package com.cleevio.fundedmind.application.module.comment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.comment.query.GetLessonCommentQuery
import com.cleevio.fundedmind.domain.comment.exception.CommentNotFoundException
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetLessonCommentQueryHandlerTest @Autowired constructor(
    private val underTest: GetLessonCommentQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should fetch a specific comment by id with thread comments`() {
        // given
        // Create app users
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT)

        // Create traders and students
        val trader1 = dataHelper.getTrader(
            id = 0.toUUID(),
            firstName = "Fratisek",
            lastName = "Papez",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            position = "Daddy",
        )
        val trader2 = dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "Hana",
            lastName = "Doe",
            badgeColor = BadgeColor.BLUE_GRADIENT,
            position = "Mentoris",
        )
        val student1 = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Lolo",
            lastName = "Sestprsty",
            gameLevel = GameLevel.FIVE,
        )
        val student2 = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "John",
            lastName = "Doe",
            gameLevel = GameLevel.SIX,
        )

        // Create lessons for courses of user 0
        dataHelper.getCourse(id = 10.toUUID(), traderId = 0.toUUID())
        dataHelper.getCourseModule(id = 100.toUUID(), courseId = 10.toUUID())
        val lesson = dataHelper.getLesson(id = 1111.toUUID(), courseModuleId = 100.toUUID())

        // Create the main comment we want to fetch
        val mainComment = dataHelper.getLessonComment(
            id = 53.toUUID(),
            appUserId = trader2.id,
            lessonId = lesson.id,
            text = "This is the main comment",
        )

        // Create thread comments for the main comment
        dataHelper.getLessonComment(
            appUserId = student1.id,
            lessonId = lesson.id,
            threadId = mainComment.id,
            text = "First thread comment",
        )
        dataHelper.getLessonComment(
            appUserId = trader1.id,
            lessonId = lesson.id,
            threadId = mainComment.id,
            text = "Second thread comment",
        )
        dataHelper.getLessonComment(
            appUserId = trader2.id,
            lessonId = lesson.id,
            threadId = mainComment.id,
            text = "Third thread comment",
        )

        // Add likes to the main comment
        dataHelper.getCommentLike(appUserId = student1.id, commentId = mainComment.id)
        dataHelper.getCommentLike(appUserId = trader1.id, commentId = mainComment.id)

        // when
        val result = underTest.handle(
            GetLessonCommentQuery(
                commentId = mainComment.id,
                lessonId = lesson.id,
                userId = trader1.id,
            ),
        )

        // then
        result.comment.run {
            id shouldBe mainComment.id
            lessonId shouldBe lesson.id
            text shouldBe "This is the main comment"
            likesCount shouldBe 2
            iLikeComment shouldBe true // trader1 liked this comment

            owner.run {
                applicationUserId shouldBe trader2.id
                firstName shouldBe "Hana"
                lastName shouldBe "Doe"
                role shouldBe UserRole.TRADER
                badgeColor shouldBe BadgeColor.BLUE_GRADIENT
                position shouldBe "Mentoris"
                gameLevel shouldBe null
                profilePicture shouldBe null
            }

            threadComments shouldHaveSize 3
            threadComments.run {
                first { it.owner.applicationUserId == student1.id }.run {
                    text shouldBe "First thread comment"
                    threadId shouldBe mainComment.id
                    owner.run {
                        firstName shouldBe "Lolo"
                        lastName shouldBe "Sestprsty"
                        role shouldBe UserRole.STUDENT
                        gameLevel shouldBe GameLevel.FIVE
                        badgeColor shouldBe null
                        position shouldBe null
                        profilePicture shouldBe null
                    }
                }

                first { it.owner.applicationUserId == trader1.id }.run {
                    text shouldBe "Second thread comment"
                    threadId shouldBe mainComment.id
                    owner.run {
                        firstName shouldBe "Fratisek"
                        lastName shouldBe "Papez"
                        role shouldBe UserRole.TRADER
                        badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
                        position shouldBe "Daddy"
                        gameLevel shouldBe null
                        profilePicture shouldBe null
                    }
                }

                first { it.owner.applicationUserId == trader2.id }.run {
                    text shouldBe "Third thread comment"
                    threadId shouldBe mainComment.id
                    owner.run {
                        firstName shouldBe "Hana"
                        lastName shouldBe "Doe"
                        role shouldBe UserRole.TRADER
                        badgeColor shouldBe BadgeColor.BLUE_GRADIENT
                        position shouldBe "Mentoris"
                        gameLevel shouldBe null
                        profilePicture shouldBe null
                    }
                }
            }
        }
    }

    @Test
    fun `should fetch a comment without thread comments`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(
            id = 0.toUUID(),
            firstName = "John",
            lastName = "Doe",
            gameLevel = GameLevel.THREE,
        )

        // Create lessons for courses of user 0
        val lesson = dataHelper.getLesson(
            id = 1111.toUUID(),
            courseModuleId = dataHelper.getCourseModule(
                id = 100.toUUID(),
                courseId = dataHelper.getCourse(
                    id = 10.toUUID(),
                    traderId = dataHelper.getTrader().also {
                        dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
                    }.id,
                ).id,
            ).id,
        )

        val comment = dataHelper.getLessonComment(
            id = 42.toUUID(),
            appUserId = student.id,
            lessonId = lesson.id,
            text = "Simple comment without threads",
        )

        // when
        val result = underTest.handle(
            GetLessonCommentQuery(
                commentId = comment.id,
                lessonId = lesson.id,
                userId = student.id,
            ),
        )

        // then
        result.comment.run {
            id shouldBe comment.id
            lessonId shouldBe lesson.id
            text shouldBe "Simple comment without threads"
            likesCount shouldBe 0
            iLikeComment shouldBe false
            threadComments shouldHaveSize 0

            owner.run {
                applicationUserId shouldBe student.id
                firstName shouldBe "John"
                lastName shouldBe "Doe"
                role shouldBe UserRole.STUDENT
                gameLevel shouldBe GameLevel.THREE
                badgeColor shouldBe null
                position shouldBe null
                profilePicture shouldBe null
            }
        }
    }

    @Test
    fun `should throw exception when comment does not exist`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID())

        val lesson = dataHelper.getLesson(
            id = 1111.toUUID(),
            courseModuleId = dataHelper.getCourseModule(
                id = 100.toUUID(),
                courseId = dataHelper.getCourse(
                    id = 10.toUUID(),
                    traderId = dataHelper.getTrader().also {
                        dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
                    }.id,
                ).id,
            ).id,
        )

        val nonExistentCommentId = 999.toUUID()

        // when & then
        shouldThrow<CommentNotFoundException> {
            underTest.handle(
                GetLessonCommentQuery(
                    commentId = nonExistentCommentId,
                    lessonId = lesson.id,
                    userId = student.id,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when comment belongs to different lesson`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID())

        val module = dataHelper.getCourseModule(
            id = 100.toUUID(),
            courseId = dataHelper.getCourse(
                id = 10.toUUID(),
                traderId = dataHelper.getTrader().also {
                    dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
                }.id,
            ).id,
        )

        val lesson1 = dataHelper.getLesson(id = 1111.toUUID(), courseModuleId = module.id)
        val lesson2 = dataHelper.getLesson(id = 2222.toUUID(), courseModuleId = module.id)

        val comment = dataHelper.getLessonComment(
            appUserId = student.id,
            lessonId = lesson1.id,
        )

        // when & then
        shouldThrow<CommentNotFoundException> {
            underTest.handle(
                GetLessonCommentQuery(
                    commentId = comment.id,
                    lessonId = lesson2.id, // Different lesson
                    userId = student.id,
                ),
            )
        }
    }
}
