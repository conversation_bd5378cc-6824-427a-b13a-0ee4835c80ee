package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.query.GetDashboardLessonToWatchQuery
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetDashboardLessonToWatchQueryHandlerTest @Autowired constructor(
    private val underTest: GetDashboardLessonToWatchQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get last watched lesson based on updatedAt DESC`() {
        val user = dataHelper.getAppUser(id = 999.toUUID())
        val user2 = dataHelper.getAppUser(id = 888.toUUID())

        val trader = dataHelper.getTrader(id = 0.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)

        dataHelper.getLesson(
            id = 1.toUUID(),
            title = "L1",
            courseModuleId = courseModule.id,
            thumbnailUrl = "thumbnailUrl",
            thumbnailAnimationUrl = "thumbnailAnimationUrl",
            durationInSeconds = 120,
        ).also { lesson ->
            dataHelper.getLessonProgress(
                id = 10.toUUID(),
                userId = user.id,
                lessonId = lesson.id,
                seconds = 100,
            )
        }

        dataHelper.getLesson(
            id = 2.toUUID(),
            title = "L2",
            courseModuleId = courseModule.id,
            thumbnailUrl = "thumbnailUrl2",
            thumbnailAnimationUrl = "thumbnailAnimationUrl2",
            durationInSeconds = 420,
        ).also { lesson ->
            dataHelper.getLessonProgress(
                id = 20.toUUID(),
                userId = user.id,
                lessonId = lesson.id,
                seconds = 20,
            )

            // different user, same lesson
            dataHelper.getLessonProgress(
                id = 21.toUUID(),
                userId = user2.id,
                lessonId = lesson.id,
                seconds = 400,
            )
        }

        val result = underTest.handle(GetDashboardLessonToWatchQuery(userId = user.id))

        result.run {
            lessonId shouldBe 2.toUUID()
            courseModuleId shouldBe 0.toUUID()
            courseId shouldBe 0.toUUID()
            title shouldBe "L2"
            thumbnailUrl shouldBe "thumbnailUrl2"
            thumbnailAnimationUrl shouldBe "thumbnailAnimationUrl2"
            durationInSeconds shouldBe 420
            watchedSeconds shouldBe 20
        }
    }

    @Test
    fun `get first lesson of basecamp category`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val trader = dataHelper.getTrader(1.toUUID())

        // deleted course
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = trader.id,
            courseCategory = CourseCategory.BASECAMP,
            listingOrder = 1,
            entityModifier = { it.softDelete() },
        )

        // first non-deleted course
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = trader.id,
            courseCategory = CourseCategory.BASECAMP,
            listingOrder = 2,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // deleted module
            dataHelper.getCourseModule(
                id = 11.toUUID(),
                courseId = course.id,
                listingOrder = 1,
                entityModifier = { it.softDelete() },
            )
            // first non-deleted module
            dataHelper.getCourseModule(
                id = 12.toUUID(),
                courseId = course.id,
                listingOrder = 2,
            ).also { module ->
                // deleted lesson
                dataHelper.getLesson(
                    id = 121.toUUID(),
                    courseModuleId = module.id,
                    listingOrder = 1,
                    entityModifier = { it.softDelete() },
                )
                // first non-deleted lesson - EXPECTED result
                dataHelper.getLesson(
                    id = 122.toUUID(),
                    courseModuleId = module.id,
                    listingOrder = 2,
                    title = "L1",
                    thumbnailUrl = "thumbnailUrl",
                    thumbnailAnimationUrl = "thumbnailAnimationUrl",
                    durationInSeconds = 420,
                )
            }
            // second non-deleted module
            dataHelper.getCourseModule(
                id = 13.toUUID(),
                courseId = course.id,
                listingOrder = 3,
            ).also { module ->
                // first non-deleted lesson
                dataHelper.getLesson(
                    id = 131.toUUID(),
                    courseModuleId = module.id,
                    listingOrder = 1,
                )
            }
        }

        // second non-deleted course
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = trader.id,
            courseCategory = CourseCategory.BASECAMP,
            listingOrder = 3,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // first non-deleted module
            dataHelper.getCourseModule(
                id = 21.toUUID(),
                courseId = course.id,
                listingOrder = 1,
            ).also { module ->
                // first non-deleted lesson
                dataHelper.getLesson(
                    id = 211.toUUID(),
                    courseModuleId = module.id,
                    listingOrder = 1,
                )
            }
        }

        // different course category
        dataHelper.getCourse(
            id = 3.toUUID(),
            traderId = trader.id,
            courseCategory = CourseCategory.EXCLUSIVE,
            listingOrder = 1,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // first non-deleted module
            dataHelper.getCourseModule(
                id = 31.toUUID(),
                courseId = course.id,
                listingOrder = 1,
            ).also { module ->
                // first non-deleted lesson
                dataHelper.getLesson(
                    id = 311.toUUID(),
                    courseModuleId = module.id,
                    listingOrder = 1,
                )
            }
        }

        // when
        val result = underTest.handle(GetDashboardLessonToWatchQuery(userId = 0.toUUID()))

        // then
        result.run {
            lessonId shouldBe 122.toUUID()
            courseModuleId shouldBe 12.toUUID()
            courseId shouldBe 1.toUUID()
            title shouldBe "L1"
            thumbnailUrl shouldBe "thumbnailUrl"
            thumbnailAnimationUrl shouldBe "thumbnailAnimationUrl"
            durationInSeconds shouldBe 420
        }
    }

    @Test
    fun `should throw if there is no non-deleted lesson in basecamp category`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val trader = dataHelper.getTrader(1.toUUID())

        // deleted course
        dataHelper.getCourse(
            traderId = trader.id,
            courseCategory = CourseCategory.BASECAMP,
            entityModifier = { it.softDelete() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id, entityModifier = { it.softDelete() })
                .also { courseModule ->
                    dataHelper.getLesson(courseModuleId = courseModule.id, entityModifier = { it.softDelete() })
                }
        }

        // when/then
        shouldThrow<LessonNotFoundException> {
            underTest.handle(GetDashboardLessonToWatchQuery(userId = 0.toUUID()))
        }
    }

    @Test
    fun `should throw if there is no published course in basecamp category`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val trader = dataHelper.getTrader(id = 1.toUUID())

        // course is not published - its lesson should not be returned
        dataHelper.getCourse(
            traderId = trader.id,
            courseCategory = CourseCategory.BASECAMP,
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
            }
        }

        // when/then
        shouldThrow<LessonNotFoundException> {
            underTest.handle(GetDashboardLessonToWatchQuery(userId = 0.toUUID()))
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
