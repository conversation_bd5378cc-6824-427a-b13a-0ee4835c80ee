package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.FirebaseUserDetail
import com.cleevio.fundedmind.application.module.user.appuser.command.SyncUserEmailCommand
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class SyncUserEmailCommandHandlerTest @Autowired constructor(
    private val underTest: SyncUserEmailCommandHandler,
    private val appUserRepository: AppUserRepository,
) : IntegrationTest() {

    @Test
    fun `should sync user email with firebase and stripe`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            stripeIdentifier = "cus_123",
            hubspotIdentifier = 1L,
        )

        every { firebaseService.getByFirebaseId("firebase-123456") } returns Result.success(
            FirebaseUserDetail(
                firebaseIdentifier = "firebase-123456",
                email = "<EMAIL>",
            ),
        )

        every { hubspotService.updateCrmUserEmail(any(), any()) } just Runs

        every { paymentCustomerPort.updateCustomerEmail("cus_123", "<EMAIL>") } just Runs

        underTest.handle(SyncUserEmailCommand(userId = 1.toUUID()))

        appUserRepository.findByIdOrNull(1.toUUID())!!.email shouldBe "<EMAIL>"

        verify {
            firebaseService.getByFirebaseId("firebase-123456")
            hubspotService.updateCrmUserEmail(1L, "<EMAIL>")
            paymentCustomerPort.updateCustomerEmail(
                customerId = "cus_123",
                newEmail = "<EMAIL>",
            )
        }
    }
}
