package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.PaymentInvoicePort
import com.cleevio.fundedmind.application.common.type.FakturoidInvoiceId
import com.cleevio.fundedmind.application.common.type.StripeInvoiceId
import com.cleevio.fundedmind.application.module.payment.command.ProcessInvoiceFinalizedCommand
import com.cleevio.fundedmind.application.module.payment.port.out.InvoicingPort
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserHasWrongRoleException
import com.cleevio.fundedmind.domain.common.constant.MonetaryCurrency
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate

class ProcessInvoiceFinalizedCommandHandlerTest @Autowired constructor(
    private val underTest: ProcessInvoiceFinalizedCommandHandler,
) : IntegrationTest() {

    @Test
    fun `process invoice finalized - should skip processing if invoice already exists`() {
        // given
        val stripeInvoiceId: StripeInvoiceId = "inv_1"
        val fakturoidInvoiceId: FakturoidInvoiceId = 1
        val customerId = "cus_1"
        val finalizedAt = LocalDate.of(2025, 1, 1)

        every { fakturoidService.findInvoiceIdByCustomId(stripeInvoiceId) } returns InvoicingPort.InvoiceResponse(
            id = fakturoidInvoiceId,
            status = "paid",
        )

        // when
        underTest.handle(
            ProcessInvoiceFinalizedCommand(
                invoiceIdentifier = stripeInvoiceId,
                customerIdentifier = customerId,
                finalizedAt = finalizedAt,
            ),
        )

        // then
        verify(exactly = 0) {
            stripeService.getInvoiceDataById(any())
            fakturoidService.createInvoice(any())
        }
        verify(exactly = 1) {
            fakturoidService.findInvoiceIdByCustomId(stripeInvoiceId)
        }
    }

    @Test
    fun `process invoice created - should create invoice`() {
        // given
        val userId = 1.toUUID()
        val stripeInvoiceId: StripeInvoiceId = "inv_1"
        val fakturoidInvoiceId: FakturoidInvoiceId = 1
        val customerId = "cus_1"
        val finalizedAt = LocalDate.of(2025, 1, 1)

        dataHelper.getAppUser(
            id = userId,
            email = "<EMAIL>",
            userRole = UserRole.STUDENT,
            stripeIdentifier = customerId,
            hubspotIdentifier = 1,
        ).also {
            dataHelper.getStudent(
                id = userId,
                studentTier = StudentTier.MASTERCLASS,
                firstName = "John",
                lastName = "Doe",
            )
        }

        every { fakturoidService.findInvoiceIdByCustomId(stripeInvoiceId) } returns null
        every { stripeService.getInvoiceDataById(stripeInvoiceId) } returns PaymentInvoicePort.InvoiceData(
            ico = "12345678",
            dic = "**********",
            addressLineFirst = "Street 123",
            addressLineSecond = null,
            city = "Prague",
            postalCode = "12345",
            countryCode = "CZ",
            products = listOf(
                PaymentInvoicePort.InvoiceData.InvoiceLine(
                    description = "Product 1",
                    unitAmount = 100,
                    currency = MonetaryCurrency.CZK,
                ),
            ),
        )

        every { fakturoidService.createInvoice(any()) } returns fakturoidInvoiceId

        // when
        underTest.handle(
            ProcessInvoiceFinalizedCommand(
                invoiceIdentifier = stripeInvoiceId,
                customerIdentifier = customerId,
                finalizedAt = finalizedAt,
            ),
        )

        // then
        verify {
            fakturoidService.findInvoiceIdByCustomId(stripeInvoiceId)
            stripeService.getInvoiceDataById(stripeInvoiceId)
            fakturoidService.createInvoice(
                InvoicingPort.InvoiceCreationRequest(
                    customId = stripeInvoiceId,
                    name = "John Doe",
                    email = "<EMAIL>",
                    street = "Street 123",
                    city = "Prague",
                    zip = "12345",
                    country = "CZ",
                    ico = "12345678",
                    dic = "**********",
                    issuedOn = "2025-01-01",
                    products = listOf(
                        InvoicingPort.InvoiceCreationRequest.Product(
                            name = "Product 1",
                            price = 1.0,
                        ),
                    ),
                ),
            )
        }
    }

    @Test
    fun `process invoice created - should throw if user is not a student`() {
        // given
        val userId = 1.toUUID()
        val invoiceId = "inv_1"
        val customerId = "cus_1"
        val finalizedAt = LocalDate.of(2025, 1, 1)

        dataHelper.getAppUser(
            id = userId,
            userRole = UserRole.TRADER,
            stripeIdentifier = customerId,
            hubspotIdentifier = 1,
        )

        every { fakturoidService.findInvoiceIdByCustomId(invoiceId) } returns null

        // when/then
        shouldThrow<UserHasWrongRoleException> {
            underTest.handle(
                ProcessInvoiceFinalizedCommand(
                    invoiceIdentifier = invoiceId,
                    customerIdentifier = customerId,
                    finalizedAt = finalizedAt,
                ),
            )
        }

        verify {
            fakturoidService.findInvoiceIdByCustomId(invoiceId)
        }
        verify(exactly = 0) {
            stripeService.getInvoiceDataById(any())
            fakturoidService.createInvoice(any())
        }
    }
}
