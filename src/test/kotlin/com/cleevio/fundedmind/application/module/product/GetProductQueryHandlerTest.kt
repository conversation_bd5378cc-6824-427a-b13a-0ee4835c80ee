package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.port.out.GetDefaultProductPricePort.ProductPrice
import com.cleevio.fundedmind.application.module.product.exception.ProductNotFoundException
import com.cleevio.fundedmind.application.module.product.query.GetProductDetailQuery
import com.cleevio.fundedmind.domain.common.constant.MonetaryCurrency
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetProductQueryHandlerTest @Autowired constructor(
    private val underTest: GetProductQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get product with stripe price`() {
        every { stripeService.getByIdentifier("prod_123") } returns
            ProductPrice(
                "prod_123",
                MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
            )

        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            name = "5x Mentoring 1-on-1",
            stripeIdentifier = "prod_123",
            description = "Dope Mentoring",
            altDescription = "Product for mentoring",
            sessionsCount = 5,
            validityInDays = 30,
        )

        val result = underTest.handle(
            GetProductDetailQuery(productId = 1.toUUID()),
        )

        result.run {
            productId shouldBe 1.toUUID()
            traderId shouldBe 1.toUUID()
            price.run {
                unitAmount shouldBe 100
                currency shouldBe MonetaryCurrency.CZK
                amount shouldBeEqualComparingTo 1.toBigDecimal()
            }
            name shouldBe "5x Mentoring 1-on-1"
            stripeIdentifier shouldBe "prod_123"
            description shouldBe "Dope Mentoring"
            altDescription shouldBe "Product for mentoring"
            sessionsCount shouldBe 5
            validityInDays shouldBe 30
        }

        verify { stripeService.getByIdentifier("prod_123") }
    }

    @Test
    fun `should throw if product does not exist`() {
        shouldThrow<ProductNotFoundException> {
            underTest.handle(
                GetProductDetailQuery(productId = 1.toUUID()),
            )
        }
    }
}
