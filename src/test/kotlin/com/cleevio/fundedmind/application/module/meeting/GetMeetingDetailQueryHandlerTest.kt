package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.query.GetMeetingDetailQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.meeting.exception.MeetingNotFoundException
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetMeetingDetailQueryHandlerTest @Autowired constructor(
    private val underTest: GetMeetingDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get meeting detail`() {
        // given

        // trader 1
        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "John",
            lastName = "Doe",
            entityModifier = {
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url1",
                        compressedFileUrl = "url1-comp",
                        blurHash = "1",
                    ).id,
                )
            },
        )

        // trader 2
        dataHelper.getTrader(
            id = 2.toUUID(),
            position = "Mentor",
            firstName = "Jane",
            lastName = "Doe",
            entityModifier = {
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url2",
                        compressedFileUrl = "url2-comp",
                        blurHash = "2",
                    ).id,
                )
            },
        )

        // trader 3
        dataHelper.getTrader(
            id = 3.toUUID(),
            position = "Mentor",
            firstName = "Jose",
            lastName = "Dolores",
        )

        // meeting
        dataHelper.getMeeting(
            id = 1.toUUID(),
            name = "Meeting 1",
            color = Color.BLUE,
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            description = "Meeting description",
            invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            invitedDiscordUsers = true,
            meetingUrl = "meeting-url",
            recordingUrl = "recording-url",
            traderIds = listOf(3.toUUID(), 1.toUUID(), 2.toUUID()),
        )

        // when
        val result = underTest.handle(
            GetMeetingDetailQuery(meetingId = 1.toUUID()),
        )

        // then
        result.meetingId shouldBe 1.toUUID()
        result.name shouldBe "Meeting 1"
        result.color shouldBe Color.BLUE
        result.startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
        result.finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
        result.description shouldBe "Meeting description"
        result.invitedTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
        result.invitedDiscordUsers shouldBe true
        result.coverPhoto shouldBe null
        result.meetingUrl shouldBe "meeting-url"
        result.recordingUrl shouldBe "recording-url"
        result.traders shouldHaveSize 3
        result.traders.run {
            this[0].run {
                traderId shouldBe 3.toUUID()
                displayOrder shouldBe 1
                position shouldBe "Mentor"
                firstName shouldBe "Jose"
                lastName shouldBe "Dolores"
                profilePicture shouldBe null
            }

            this[1].run {
                traderId shouldBe 1.toUUID()
                displayOrder shouldBe 2
                position shouldBe "Mentor"
                firstName shouldBe "John"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url1"
                    imageCompressedUrl shouldBe "url1-comp"
                    imageBlurHash shouldBe "1"
                }
            }

            this[2].run {
                traderId shouldBe 2.toUUID()
                displayOrder shouldBe 3
                position shouldBe "Mentor"
                firstName shouldBe "Jane"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url2"
                    imageCompressedUrl shouldBe "url2-comp"
                    imageBlurHash shouldBe "2"
                }
            }
        }
    }

    @Test
    fun `should throw if meeting does not exist`() {
        shouldThrow<MeetingNotFoundException> {
            underTest.handle(
                GetMeetingDetailQuery(meetingId = 1.toUUID()),
            )
        }
    }
}
