package com.cleevio.fundedmind.application.module.gametournament

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gametournament.exception.GameTournamentNotFoundException
import com.cleevio.fundedmind.application.module.gametournament.query.GetGameTournamentQuery
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate

class GetGameTournamentQueryHandlerTest @Autowired constructor(
    private val underTest: GetGameTournamentQueryHandler,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "2025; 300.25",
            "2025; -300.25",
        ],
    )
    fun `should return game tournament for specified day`(
        year: Int,
        payoutOffset: BigDecimal,
    ) {
        // Given
        dataHelper.getGameTournament(
            id = 1.toUUID(),
            startAt = LocalDate.of(2025, 1, 1), // 01.01.2025
            finishAt = LocalDate.of(2025, 6, 30), // 30.06.2025
            payoutOffset = payoutOffset,
        )

        // When
        val result = underTest.handle(
            GetGameTournamentQuery(
                gameTournamentId = null,
                today = LocalDate.of(2025, 3, 1), // 01.03.2025
            ),
        )

        // Then
        result.run {
            this.gameTournamentId shouldBe 1.toUUID()
            this.year shouldBe year
            this.startAt shouldBe LocalDate.of(2025, 1, 1)
            this.finishAt shouldBe LocalDate.of(2025, 6, 30)
            this.payoutOffset shouldBeEqualComparingTo payoutOffset
        }
    }

    @Test
    fun `should throw exception when no game tournament exists`() {
        // No game tournament created

        // When/Then
        shouldThrow<GameTournamentNotFoundException> {
            underTest.handle(
                GetGameTournamentQuery(
                    gameTournamentId = null,
                    today = LocalDate.of(2025, 3, 1),
                ),
            )
        }

        shouldThrow<GameTournamentNotFoundException> {
            underTest.handle(
                GetGameTournamentQuery(
                    gameTournamentId = 1.toUUID(),
                    today = LocalDate.of(2025, 3, 1),
                ),
            )
        }
    }
}
