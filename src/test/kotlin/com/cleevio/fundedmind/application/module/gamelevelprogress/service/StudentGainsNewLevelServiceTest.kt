package com.cleevio.fundedmind.application.module.gamelevelprogress.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamelevelprogress.GameLevelProgressRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentGainsNewLevelServiceTest @Autowired constructor(
    private val underTest: StudentGainsNewLevelService,
    private val gameLevelProgressRepository: GameLevelProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should create new game level progress for student`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())

        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs

        // When
        underTest.studentGainsNewLevel(
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
        )

        // Then
        val progresses = gameLevelProgressRepository.findAllByStudentId(student.id)
        progresses shouldHaveSize 1
        progresses.single().run {
            studentId shouldBe 1.toUUID()
            gameLevel shouldBe GameLevel.FIVE
            achievedAt shouldBe "2024-01-01T10:00:00Z".toInstant()
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.FIVE) }
    }

    @Test
    fun `should not create new game level progress if student already has the level`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())

        // Create existing progress
        dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
        )

        // When
        underTest.studentGainsNewLevel(
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-02T10:00:00Z".toInstant(), // Different time
        )

        // Then
        val progresses = gameLevelProgressRepository.findAllByStudentId(student.id)
        progresses shouldHaveSize 1 // Still only one progress
        progresses.single().run {
            studentId shouldBe 1.toUUID()
            gameLevel shouldBe GameLevel.FIVE
            achievedAt shouldBe "2024-01-01T10:00:00Z".toInstant() // Original time preserved
        }
    }

    @Test
    fun `should create different game level progress for same student`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())

        // Create existing progress for level FOUR
        dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FOUR,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
        )

        every { sendEmailService.sendEmailGameLevelGained(any(), any()) } just Runs

        // When
        underTest.studentGainsNewLevel(
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-02T10:00:00Z".toInstant(),
        )

        // Then
        val progresses = gameLevelProgressRepository.findAllByStudentId(student.id)
        progresses shouldHaveSize 2 // Now two progresses

        // Verify both levels exist
        progresses.any { it.gameLevel == GameLevel.FOUR } shouldBe true
        progresses.any { it.gameLevel == GameLevel.FIVE } shouldBe true

        verify { sendEmailService.sendEmailGameLevelGained(1.toUUID(), GameLevel.FIVE) }
    }
}
