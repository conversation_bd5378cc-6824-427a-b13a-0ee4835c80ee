package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.CreateNewCourseCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.course.exception.CourseHomepageRequiresPublicException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class CreateNewCourseCommandHandlerTest @Autowired constructor(
    private val underTest: CreateNewCourseCommandHandler,
    private val courseRepository: CourseRepository,
) : IntegrationTest() {

    @Test
    fun `should create new course`() {
        dataHelper.getTrader(9.toUUID())

        val result = underTest.handle(
            CreateNewCourseCommand(
                title = "Course",
                courseCategory = CourseCategory.TRADING_BASICS,
                visibleToTiers = listOf(StudentTier.MASTERCLASS),
                visibleToDiscordUsers = true,
                description = "Description",
                traderId = 9.toUUID(),
                color = Color.GREEN,
                thumbnailUrl = "thumbnailUrl",
                thumbnailAnimationUrl = "thumbnailAnimationUrl",
                trailerUrl = "trailerUrl",
                public = true,
                homepage = true,
            ),
        )

        courseRepository.findByIdOrNull(result.id)!!.run {
            listingOrder shouldBe 1
            title shouldBe "Course"
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            visibleToTiers shouldBe listOf(StudentTier.MASTERCLASS)
            visibleToDiscordUsers shouldBe true
            description shouldBe "Description"
            traderId shouldBe 9.toUUID()
            color shouldBe Color.GREEN
            thumbnailUrl shouldBe "thumbnailUrl"
            thumbnailAnimationUrl shouldBe "thumbnailAnimationUrl"
            trailerUrl shouldBe "trailerUrl"
        }
    }

    @Test
    fun `should throw if trader does not exist`() {
        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                defaultCommand(traderId = 999.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if on homepage but not public`() {
        dataHelper.getTrader(1.toUUID())

        shouldThrow<CourseHomepageRequiresPublicException> {
            underTest.handle(
                defaultCommand(
                    traderId = 1.toUUID(),
                    public = false,
                    homepage = true,
                ),
            )
        }
    }

    @Test
    fun `should create course and set its listing order as first in category and reorder other courses in category`() {
        dataHelper.getTrader(1.toUUID())

        dataHelper.getCourse(
            id = 11.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
        )
        dataHelper.getCourse(
            id = 12.toUUID(),
            listingOrder = 2,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
        )
        dataHelper.getCourse(
            id = 21.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE, // different category
        )
        dataHelper.getCourse(
            id = 22.toUUID(),
            listingOrder = 2,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE, // different category
        )

        val result = underTest.handle(
            defaultCommand(
                traderId = 1.toUUID(),
                courseCategory = CourseCategory.BASECAMP,
            ),
        )

        courseRepository.findByIdOrNull(result.id)!!.listingOrder shouldBe 1
        courseRepository.findAll().run {
            size shouldBe 5
            // basecamp category
            first { it.id == result.id }.listingOrder shouldBe 1
            first { it.id == 11.toUUID() }.listingOrder shouldBe 2
            first { it.id == 12.toUUID() }.listingOrder shouldBe 3
            // exclusive category
            first { it.id == 21.toUUID() }.listingOrder shouldBe 1
            first { it.id == 22.toUUID() }.listingOrder shouldBe 2
        }
    }

    @Test
    fun `should create course and reorder non-deleted courses within category + set new course as first`() {
        dataHelper.getTrader(1.toUUID())
        dataHelper.getCourse(1.toUUID(), listingOrder = 1, traderId = 1.toUUID())
        dataHelper.getCourse(2.toUUID(), listingOrder = 2, traderId = 1.toUUID())
        dataHelper.getCourse(3.toUUID(), listingOrder = 3, traderId = 1.toUUID(), entityModifier = { it.softDelete() })
        dataHelper.getCourse(4.toUUID(), listingOrder = 3, traderId = 1.toUUID())

        val result = underTest.handle(
            defaultCommand(
                traderId = 1.toUUID(),
            ),
        )

        courseRepository.findAll().run {
            size shouldBe 5
            first { it.id == result.id }.listingOrder shouldBe 1
            first { it.id == 1.toUUID() }.listingOrder shouldBe 2
            first { it.id == 2.toUUID() }.listingOrder shouldBe 3
            first { it.id == 3.toUUID() }.listingOrder shouldBe 3 // deleted course is not reordered
            first { it.id == 4.toUUID() }.listingOrder shouldBe 4
        }
    }

    private fun defaultCommand(
        traderId: UUID,
        title: String = "Course",
        courseCategory: CourseCategory = CourseCategory.TRADING_BASICS,
        visibleToTiers: List<StudentTier> = listOf(StudentTier.MASTERCLASS),
        visibleToDiscordUsers: Boolean = true,
        description: String = "Description",
        color: Color = Color.GREEN,
        thumbnailUrl: String = "thumbnailUrl",
        thumbnailAnimationUrl: String = "thumbnailAnimationUrl",
        trailerUrl: String = "trailerUrl",
        public: Boolean = true,
        homepage: Boolean = true,
    ) = CreateNewCourseCommand(
        title = title,
        courseCategory = courseCategory,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        description = description,
        traderId = traderId,
        color = color,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        trailerUrl = trailerUrl,
        public = public,
        homepage = homepage,
    )
}
