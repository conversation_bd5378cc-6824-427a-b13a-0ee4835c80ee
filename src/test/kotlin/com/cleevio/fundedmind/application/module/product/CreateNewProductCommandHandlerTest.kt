package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.module.product.command.CreateNewProductCommand
import com.cleevio.fundedmind.application.module.product.exception.ProductAlreadyExistsException
import com.cleevio.fundedmind.application.module.product.exception.ProductNotFoundException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.product.ProductRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class CreateNewProductCommandHandlerTest @Autowired constructor(
    private val underTest: CreateNewProductCommandHandler,
    private val productRepository: ProductRepository,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        nullValues = ["null"],
        value = ["30", "null"],
    )
    fun `should create new product with validity days`(givenValidity: Int?) {
        dataHelper.getTrader(id = 1.toUUID())

        every { existsExternalProductPort.existsByIdentifier("prod_123") } returns true

        val result = underTest.handle(
            defaultCommand(
                traderId = 1.toUUID(),
                name = "5x Mentoring 1-on-1",
                stripeIdentifier = "prod_123",
                description = "Dope Mentoring",
                altDescription = "Product for mentoring",
                sessionsCount = 5,
                validityInDays = givenValidity,
            ),
        )

        productRepository.findByIdOrNull(result.id)!!.run {
            name shouldBe "5x Mentoring 1-on-1"
            stripeIdentifier shouldBe "prod_123"
            description shouldBe "Dope Mentoring"
            altDescription shouldBe "Product for mentoring"
            sessionsCount shouldBe 5
            traderId shouldBe 1.toUUID()
            validityInDays shouldBe givenValidity
            saleable shouldBe true
        }

        verify { existsExternalProductPort.existsByIdentifier("prod_123") }
    }

    @Test
    fun `should throw if trader does not exist`() {
        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                defaultCommand(traderId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if external product is not verified`() {
        dataHelper.getTrader(id = 1.toUUID())

        every { existsExternalProductPort.existsByIdentifier("prod_123") } returns false

        shouldThrow<ProductNotFoundException> {
            underTest.handle(
                defaultCommand(
                    traderId = 1.toUUID(),
                    stripeIdentifier = "prod_123",
                ),
            )
        }

        verify { existsExternalProductPort.existsByIdentifier("prod_123") }
    }

    @Test
    fun `should throw if product with stripe identifier already exists`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getProduct(traderId = 1.toUUID(), stripeIdentifier = "prod_123")

        shouldThrow<ProductAlreadyExistsException> {
            underTest.handle(
                defaultCommand(
                    traderId = 1.toUUID(),
                    stripeIdentifier = "prod_123",
                ),
            )
        }
    }

    private fun defaultCommand(
        name: String = "5x Mentoring 1-on-1",
        stripeIdentifier: StripeProductId = "prod_123",
        description: String = "Dope Mentoring",
        altDescription: String = "Product for mentoring",
        sessionsCount: Int = 5,
        traderId: UUID,
        validityInDays: Int? = null,
    ) = CreateNewProductCommand(
        name = name,
        stripeIdentifier = stripeIdentifier,
        description = description,
        altDescription = altDescription,
        sessionsCount = sessionsCount,
        traderId = traderId,
        validityInDays = validityInDays,
    )
}
