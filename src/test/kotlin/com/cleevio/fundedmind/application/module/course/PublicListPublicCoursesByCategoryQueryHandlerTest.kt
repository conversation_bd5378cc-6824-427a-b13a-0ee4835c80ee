package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.PublicListPublicCoursesByCategoryQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class PublicListPublicCoursesByCategoryQueryHandlerTest @Autowired constructor(
    private val underTest: PublicListPublicCoursesByCategoryQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list public courses - verify mappings`() {
        // given
        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )
        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            title = "Course",
            description = "Description",
            thumbnailUrl = "thumbnail-url",
            thumbnailAnimationUrl = "thumbnail-animation-url",
            color = Color.ORANGE,
            courseCategory = CourseCategory.TRADING_BASICS,
            homepage = true,
            public = true,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                )
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                )
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                    entityModifier = { it.softDelete() },
                )
            }
            // module with 1 lesson
            dataHelper.getCourseModule(courseId = course.id).also { module2 ->
                dataHelper.getLesson(
                    courseModuleId = module2.id,
                    durationInSeconds = 100,
                )
            }
            // module with 1 deleted lesson
            dataHelper.getCourseModule(courseId = course.id).also { module3 ->
                dataHelper.getLesson(
                    courseModuleId = module3.id,
                    durationInSeconds = 1000,
                    entityModifier = { it.softDelete() },
                )
            }
            // deleted module with deleted lesson
            dataHelper.getCourseModule(
                id = 4.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module4 ->
                dataHelper.getLesson(
                    courseModuleId = module4.id,
                    durationInSeconds = 10000,
                    entityModifier = { it.softDelete() },
                )
            }
        }

        // when
        val result = underTest.handle(
            PublicListPublicCoursesByCategoryQuery(
                withRefresh = false,
                filter = PublicListPublicCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            courseId shouldBe 1.toUUID()
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            listingOrder shouldBe 1
            traderBio shouldNotBe null
            traderBio.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            }
            color shouldBe Color.ORANGE
            thumbnailUrl shouldBe "thumbnail-url"
            thumbnailAnimationUrl shouldBe "thumbnail-animation-url"
            lessonCount shouldBe 3
            totalDurationInSeconds shouldBe 120
            title shouldBe "Course"
            description shouldBe "Description"
            homepage shouldBe true
        }
    }

    @Test
    fun `should list public courses - list only published and public courses`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())

        // Published and public course
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            public = true,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }
        }

        // Published but not public course
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            public = false,
            homepage = false,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }
        }

        // Not published but public course
        dataHelper.getCourse(
            id = 3.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            public = true,
            entityModifier = { it.hide() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }
        }

        // when
        val result = underTest.handle(
            PublicListPublicCoursesByCategoryQuery(
                withRefresh = false,
                filter = PublicListPublicCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        // then
        result.data.map { it.courseId } shouldBe listOf(1.toUUID())
        result.data.first { it.courseId == 1.toUUID() }.run {
            lessonCount shouldBe 2
            totalDurationInSeconds shouldBe 20
        }
    }

    @Test
    fun `should list public courses - filter out deleted courses and return only given category`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())

        // Published and public course in BASECAMP category
        dataHelper.getCourse(
            id = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
            traderId = 1.toUUID(),
            public = true,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 10)
            }
        }

        // Deleted course in BASECAMP category
        dataHelper.getCourse(
            id = 2.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
            traderId = 1.toUUID(),
            public = true,
            entityModifier = {
                it.createPicturesAndPublish()
                it.softDelete()
            },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 100)
            }
        }

        // Published and public course in TRADING_BASICS category
        dataHelper.getCourse(
            id = 3.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
            traderId = 1.toUUID(),
            public = true,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id, durationInSeconds = 1000)
            }
        }

        // when
        val result = underTest.handle(
            PublicListPublicCoursesByCategoryQuery(
                withRefresh = false,
                filter = PublicListPublicCoursesByCategoryQuery.Filter(
                    courseCategory = CourseCategory.BASECAMP,
                ),
            ),
        )

        // then
        result.data.map { it.courseId } shouldBe listOf(1.toUUID())
        result.data.first { it.courseId == 1.toUUID() }.run {
            lessonCount shouldBe 1
            totalDurationInSeconds shouldBe 10
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
