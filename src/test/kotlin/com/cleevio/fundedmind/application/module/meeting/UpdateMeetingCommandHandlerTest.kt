package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.command.UpdateMeetingCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.meeting.MeetingRepository
import com.cleevio.fundedmind.domain.meeting.exception.MeetingNotFoundException
import com.cleevio.fundedmind.domain.meeting.exception.MeetingTimeIsIncorrectException
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.TraderInMeetingRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.Instant
import java.util.UUID

class UpdateMeetingCommandHandlerTest @Autowired constructor(
    private val underTest: UpdateMeetingCommandHandler,
    private val meetingRepository: MeetingRepository,
    private val traderInMeetingRepository: TraderInMeetingRepository,
) : IntegrationTest() {

    @Test
    fun `should update meeting`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())
        dataHelper.getTrader(id = 3.toUUID())

        dataHelper.getMeeting(
            id = 1.toUUID(),
            name = "Meeting Old",
            color = Color.RED,
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            description = "Old Meeting description",
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
            meetingUrl = "old-meeting-url",
            recordingUrl = "old-recording-url",
            traderIds = listOf(1.toUUID(), 2.toUUID()),
        )

        underTest.handle(
            UpdateMeetingCommand(
                meetingId = 1.toUUID(),
                name = "Meeting New",
                color = Color.BLUE,
                startAt = "2025-01-01T10:00:00Z".toInstant(),
                finishAt = "2025-01-01T12:00:00Z".toInstant(),
                traderIds = listOf(3.toUUID()),
                description = "New Meeting description",
                invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
                invitedDiscordUsers = true,
                meetingUrl = "new-meeting-url",
                recordingUrl = "new-recording-url",
            ),
        )

        val meeting = meetingRepository.findByIdOrNull(1.toUUID())!!

        meeting.run {
            name shouldBe "Meeting New"
            color shouldBe Color.BLUE
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            description shouldBe "New Meeting description"
            invitedTiers shouldBe listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            invitedDiscordUsers shouldBe true
            meetingUrl shouldBe "new-meeting-url"
            recordingUrl shouldBe "new-recording-url"
            coverPhotoFileId shouldBe null
        }

        val tradersInMeeting = traderInMeetingRepository.findAll()
        tradersInMeeting shouldHaveSize 1
        tradersInMeeting.first { it.traderId == 3.toUUID() }.run {
            meetingId shouldBe 1.toUUID()
            displayOrder shouldBe 1
        }
    }

    @Test
    fun `should throw if meeting does not exist`() {
        shouldThrow<MeetingNotFoundException> {
            underTest.handle(
                defaultCommand(
                    meetingId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if start time is after finish time`() {
        dataHelper.getMeeting(id = 1.toUUID())

        shouldThrow<MeetingTimeIsIncorrectException> {
            underTest.handle(
                defaultCommand(
                    meetingId = 1.toUUID(),
                    startAt = "2025-01-01T12:00:00Z".toInstant(),
                    finishAt = "2025-01-01T10:00:00Z".toInstant(),
                ),
            )
        }
    }

    @Test
    fun `should throw if trader does not exist`() {
        dataHelper.getMeeting(id = 1.toUUID())

        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                defaultCommand(
                    meetingId = 1.toUUID(),
                    traderIds = listOf(1.toUUID()),
                ),
            )
        }
    }

    private fun defaultCommand(
        meetingId: UUID,
        name: String = "Meeting 1",
        color: Color = Color.BLUE,
        startAt: Instant = "2025-01-01T10:00:00Z".toInstant(),
        finishAt: Instant = "2025-01-01T12:00:00Z".toInstant(),
        description: String = "Meeting description",
        invitedTiers: List<StudentTier> = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
        invitedDiscordUsers: Boolean = true,
        meetingUrl: String = "meeting-url",
        recordingUrl: String = "recording-url",
        traderIds: List<UUID> = listOf(),
    ) = UpdateMeetingCommand(
        meetingId = meetingId,
        name = name,
        color = color,
        startAt = startAt,
        finishAt = finishAt,
        traderIds = traderIds,
        description = description,
        invitedTiers = invitedTiers,
        invitedDiscordUsers = invitedDiscordUsers,
        meetingUrl = meetingUrl,
        recordingUrl = recordingUrl,
    )
}
