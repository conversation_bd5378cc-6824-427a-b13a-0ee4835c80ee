package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.query.GetModuleLessonPlaylistQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseIsLockedForUserException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFoundException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetModuleLessonPlaylistQueryHandlerTest @Autowired constructor(
    private val underTest: GetModuleLessonPlaylistQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get module lesson playlist - verify mappings`() {
        // given
        val user1 = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val user2 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val trader = dataHelper.getTrader()

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = trader.id,
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            color = Color.GREEN_LIGHT,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            title = "Module 1",
            description = "Description",
            shortDescription = "Short Description",
        )

        // lesson started by queried user and different user
        val lesson1 = dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 1,
            title = "First Lesson",
            durationInSeconds = 3000,
            thumbnailUrl = "thumbnail-url-1",
            thumbnailAnimationUrl = "thumbnail-animation-url-1",
        ).also { lesson ->
            dataHelper.getLessonProgress(
                id = 10.toUUID(),
                userId = user1.id,
                lessonId = lesson.id,
                seconds = 100,
            )
            dataHelper.getLessonProgress(
                id = 11.toUUID(),
                userId = user2.id, // different user
                lessonId = lesson.id,
                seconds = 200,
            )
        }
        // lesson finished by queried user
        val lesson2 = dataHelper.getLesson(
            id = 2.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 2,
            title = "Second Lesson",
            durationInSeconds = 2000,
            thumbnailUrl = "thumbnail-url-2",
            thumbnailAnimationUrl = "thumbnail-animation-url-2",
        ).also { lesson ->
            dataHelper.getLessonProgress(
                id = 20.toUUID(),
                userId = user1.id,
                lessonId = lesson.id,
                seconds = 1995,
                entityModifier = { it.finish() },
            )
        }
        // lesson finished by different user
        val lesson3 = dataHelper.getLesson(
            id = 3.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 3,
            title = "Third Lesson",
            durationInSeconds = 1000,
            thumbnailUrl = "thumbnail-url-3",
            thumbnailAnimationUrl = "thumbnail-animation-url-3",
        ).also { lesson ->
            dataHelper.getLessonProgress(
                id = 31.toUUID(),
                userId = user2.id, // different user
                lessonId = lesson.id,
                seconds = 10,
            )
        }

        val result = underTest.handle(
            GetModuleLessonPlaylistQuery(
                userId = user1.id,
                courseId = course.id,
                courseModuleId = courseModule.id,
            ),
        )

        result.run {
            moduleId shouldBe 1.toUUID()
            moduleTitle shouldBe "Module 1"
            moduleDescription shouldBe "Description"
            moduleShortDescription shouldBe "Short Description"
            lessonCount shouldBe 3
            totalDurationInSeconds shouldBe 6000
            courseColor shouldBe Color.GREEN_LIGHT
            lessons shouldHaveSize 3
            lessons[0].run {
                lessonId shouldBe 1.toUUID()
                listingOrder shouldBe 1
                thumbnailUrl shouldBe "thumbnail-url-1"
                thumbnailAnimationUrl shouldBe "thumbnail-animation-url-1"
                title shouldBe "First Lesson"
                finished shouldBe false
                durationInSeconds shouldBe 3000
                watchedSeconds shouldBe 100
            }
            lessons[1].run {
                lessonId shouldBe 2.toUUID()
                listingOrder shouldBe 2
                thumbnailUrl shouldBe "thumbnail-url-2"
                thumbnailAnimationUrl shouldBe "thumbnail-animation-url-2"
                title shouldBe "Second Lesson"
                finished shouldBe true
                durationInSeconds shouldBe 2000
                watchedSeconds shouldBe 1995
            }
            lessons[2].run {
                lessonId shouldBe 3.toUUID()
                listingOrder shouldBe 3
                thumbnailUrl shouldBe "thumbnail-url-3"
                thumbnailAnimationUrl shouldBe "thumbnail-animation-url-3"
                title shouldBe "Third Lesson"
                finished shouldBe false
                durationInSeconds shouldBe 1000
                watchedSeconds shouldBe 0
            }
        }
    }

    @Test
    fun `should throw if course was not published`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })

        // course is not published - its module and lessons should not be returned
        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
        )
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(courseModuleId = courseModule.id)

        // when/then
        shouldThrow<CourseModuleNotFoundException> {
            underTest.handle(
                GetModuleLessonPlaylistQuery(
                    userId = user.id,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            )
        }
    }

    @Test
    fun `should throw if course module is not accessible to user`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val courseModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id)

        // when/then
        shouldThrow<CourseIsLockedForUserException> {
            underTest.handle(
                GetModuleLessonPlaylistQuery(
                    userId = user.id,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            )
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
