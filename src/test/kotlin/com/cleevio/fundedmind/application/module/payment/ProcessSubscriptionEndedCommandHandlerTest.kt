package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionEndedCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ProcessSubscriptionEndedCommandHandlerTest @Autowired constructor(
    private val underTest: ProcessSubscriptionEndedCommandHandler,
    private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `process subscription ended - should deactivate discord subscription`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            hubspotIdentifier = 1,
        ).also {
            dataHelper.getStudent(
                id = 1.toUUID(),
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = {
                    it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                },
            )
        }

        every { sendEmailService.sendEmailDiscordEnded(1.toUUID()) } just Runs

        // when
        underTest.handle(
            ProcessSubscriptionEndedCommand(
                subscriptionIdentifier = "sub_1",
                customerIdentifier = "cus_1",
                productIdentifier = "prod_DISCORD",
            ),
        )

        // then
        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            discordSubscription shouldBe false
            discordSubscriptionExpiresAt shouldBe "2025-02-01T23:59:00Z".toInstant()
        }

        verify { sendEmailService.sendEmailDiscordEnded(1.toUUID()) }
    }
}
