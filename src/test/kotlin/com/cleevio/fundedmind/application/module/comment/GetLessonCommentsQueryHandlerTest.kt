package com.cleevio.fundedmind.application.module.comment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fundedmind.application.module.comment.query.GetLessonCommentsQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetLessonCommentsQueryHandlerTest @Autowired constructor(
    private val underTest: GetLessonCommentsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should fetch all comments for a lesson`() {
        // given
        // Create app users
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT)

        // Create traders and students
        val trader1 = dataHelper.getTrader(
            id = 0.toUUID(),
            firstName = "Fratisek",
            lastName = "Papez",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            position = "Daddy",
        )
        val trader2 = dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "Hana",
            lastName = "Doe",
            badgeColor = BadgeColor.BLUE_GRADIENT,
            position = "Mentoris",
        )
        val student1 = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Lolo",
            lastName = "Sestprsty",
            gameLevel = GameLevel.FIVE,
        )
        val student2 = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "John",
            lastName = "Doe",
            gameLevel = GameLevel.SIX,
        )

        // Create lessons for courses of user 0
        dataHelper.getCourse(id = 10.toUUID(), traderId = 0.toUUID())
        dataHelper.getCourseModule(id = 100.toUUID(), courseId = 10.toUUID())
        dataHelper.getLesson(id = 1111.toUUID(), courseModuleId = 100.toUUID()).also { lesson ->
            dataHelper.getLessonComment(appUserId = trader1.id, lessonId = lesson.id)
            dataHelper.getLessonComment(
                id = 45.toUUID(),
                appUserId = student1.id,
                lessonId = lesson.id,
                text = "I like this lesson",
            )
            // Create comment with thread from trader2
            dataHelper.getLessonComment(
                id = 53.toUUID(),
                appUserId = trader2.id,
                lessonId = lesson.id,
            ).also {
                // Thread comments
                dataHelper.getLessonComment(appUserId = student1.id, lessonId = lesson.id, threadId = it.id)
                dataHelper.getLessonComment(appUserId = trader1.id, lessonId = lesson.id, threadId = it.id)
                dataHelper.getLessonComment(appUserId = trader2.id, lessonId = lesson.id, threadId = it.id)
            }

            // Create liked comment of student2
            dataHelper.getLessonComment(
                id = 55.toUUID(),
                appUserId = student2.id,
                lessonId = lesson.id,
            ).also {
                // Add likes to this comment
                dataHelper.getCommentLike(appUserId = student1.id, commentId = it.id)
                dataHelper.getCommentLike(appUserId = trader1.id, commentId = it.id)
            }
        }
        dataHelper.getLesson(id = 2222.toUUID(), courseModuleId = 100.toUUID()).also { lesson ->
            // Create comments for lesson 2222
            dataHelper.getLessonComment(appUserId = student1.id, lessonId = lesson.id)
            dataHelper.getLessonComment(appUserId = trader1.id, lessonId = lesson.id)
        }

        // when
        val result = underTest.handle(
            GetLessonCommentsQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                lessonId = 1111.toUUID(),
                userId = trader1.id,
            ),
        )

        // then
        result.content.size shouldBe 4
        result.content.first { it.comment.id == 45.toUUID() }.comment.run {
            lessonId shouldBe 1111.toUUID()
            likesCount shouldBe 0
            iLikeComment shouldBe false
            threadComments.size shouldBe 0
            text shouldBe "I like this lesson"
        }

        result.content.first { it.comment.id == 55.toUUID() }.comment.run {
            lessonId shouldBe 1111.toUUID()
            owner.run {
                firstName shouldBe "John"
                lastName shouldBe "Doe"
                role shouldBe UserRole.STUDENT
                position shouldBe null
                badgeColor shouldBe null
                gameLevel shouldBe GameLevel.SIX
            }
            likesCount shouldBe 2
            iLikeComment shouldBe true
            threadComments.size shouldBe 0
        }

        result.content.first { it.comment.id == 53.toUUID() }.comment.run {
            lessonId shouldBe 1111.toUUID()
            owner.run {
                firstName shouldBe "Hana"
                lastName shouldBe "Doe"
                role shouldBe UserRole.TRADER
                badgeColor shouldBe BadgeColor.BLUE_GRADIENT
                position shouldBe "Mentoris"
                gameLevel shouldBe null
            }
            likesCount shouldBe 0
            iLikeComment shouldBe false
            threadComments shouldHaveSize 3
            threadComments.run {
                first { it.owner.applicationUserId == student1.id }.run {
                    owner.run {
                        firstName shouldBe "Lolo"
                        lastName shouldBe "Sestprsty"
                        profilePicture shouldBe null
                        badgeColor shouldBe null
                        position shouldBe null
                        role shouldBe UserRole.STUDENT
                        gameLevel shouldBe GameLevel.FIVE
                    }
                }

                first { it.owner.applicationUserId == trader1.id }.run {
                    owner.run {
                        firstName shouldBe "Fratisek"
                        lastName shouldBe "Papez"
                        profilePicture shouldBe null
                        badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
                        position shouldBe "Daddy"
                        role shouldBe UserRole.TRADER
                        gameLevel shouldBe null
                    }
                }

                first { it.owner.applicationUserId == trader2.id }.run {
                    owner.run {
                        firstName shouldBe "Hana"
                        lastName shouldBe "Doe"
                        profilePicture shouldBe null
                        position shouldBe "Mentoris"
                        badgeColor shouldBe BadgeColor.BLUE_GRADIENT
                        role shouldBe UserRole.TRADER
                        gameLevel shouldBe null
                    }
                }
            }
        }
    }
}
