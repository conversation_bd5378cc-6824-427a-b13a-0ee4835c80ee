package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.product.command.UpdateProductSaleabilityCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderAccountNotActiveException
import com.cleevio.fundedmind.domain.product.ProductRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class UpdateProductSaleabilityCommandHandlerTest @Autowired constructor(
    private val underTest: UpdateProductSaleabilityCommandHandler,
    private val productRepository: ProductRepository,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        value = [
            "true, true",
            "false, true",
            "true, false",
            "false, false",
        ],
    )
    fun `should update product saleability`(
        previousSaleable: Boolean,
        newSaleable: Boolean,
    ) {
        dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            entityModifier = {
                if (previousSaleable) {
                    it.makeSaleable()
                } else {
                    it.makeUnsaleable()
                }
            },
        )

        underTest.handle(
            UpdateProductSaleabilityCommand(
                productId = 1.toUUID(),
                saleable = newSaleable,
            ),
        )

        productRepository.findByIdOrNull(1.toUUID())!!.saleable shouldBe newSaleable
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should throw if trader account is not active`(newSaleable: Boolean) {
        dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(
                id = it.id,
                userRole = UserRole.TRADER,
                entityModifier = { it.disableAccount() },
            )
        }

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            entityModifier = { it.makeSaleable() },
        )

        shouldThrow<TraderAccountNotActiveException> {
            underTest.handle(
                UpdateProductSaleabilityCommand(
                    productId = 1.toUUID(),
                    saleable = newSaleable,
                ),
            )
        }
    }
}
