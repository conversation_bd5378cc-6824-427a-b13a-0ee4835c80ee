package com.cleevio.fundedmind.application.module.gamepayout

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.util.toStartOfTheDay
import com.cleevio.fundedmind.application.module.gamepayout.query.GetGameLeaderboardQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.Student
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class GameLeaderboardQueryHandlerTest @Autowired constructor(
    private val underTest: GameLeaderboardQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return leaderboard with shared positions with user in or out of top 10`() {
        // Given
        // Create a game tournament for 2025
        dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 12, 31),
        )

        // Create 15 students with different payout amounts for 2025
        // Students 3 and 4 will share position 3 due to equal payout amounts
        student(id = 1.toUUID(), first = "Alice", last = "1", payout = 1000.toBigDecimal(), city = "Prague")
        student(id = 2.toUUID(), first = "Bob", last = "2", payout = 950.00.toBigDecimal(), city = "Brno")
        student(id = 3.toUUID(), first = "Charlie", last = "3", payout = 800.00.toBigDecimal(), city = "Ostrava")
        student(id = 4.toUUID(), first = "Delta", last = "4", payout = 800.00.toBigDecimal())
        student(id = 5.toUUID(), first = "Edward", last = "5", payout = 750.00.toBigDecimal())
        student(id = 6.toUUID(), first = "Fiona", last = "6", payout = 700.00.toBigDecimal())
        student(id = 7.toUUID(), first = "George", last = "7", payout = 650.00.toBigDecimal())
        student(id = 8.toUUID(), first = "Hannah", last = "8", payout = 600.00.toBigDecimal())
        student(id = 9.toUUID(), first = "Ian", last = "9", payout = 550.00.toBigDecimal())
        student(id = 10.toUUID(), first = "Julia", last = "10", payout = 500.00.toBigDecimal())
        student(id = 11.toUUID(), first = "Kevin", last = "11", payout = 450.00.toBigDecimal())
        student(id = 12.toUUID(), first = "Laura", last = "12", payout = 400.00.toBigDecimal())
        student(id = 13.toUUID(), first = "Michael", last = "13", payout = 350.00.toBigDecimal())
        student(id = 14.toUUID(), first = "Nina", last = "14", payout = 300.00.toBigDecimal())
        student(id = 15.toUUID(), first = "Oscar", last = "15", payout = 250.00.toBigDecimal())

        // When
        val resultWithStudentInTop10 = underTest.handle(
            GetGameLeaderboardQuery(
                userId = 1.toUUID(), // user in top 10
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        val resultWithStudentOutsideTop10 = underTest.handle(
            GetGameLeaderboardQuery(
                userId = 15.toUUID(), // user outside top 10
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        // Then
        // Verify top 10 players are returned in correct order with proper positions
        // Players with equal payouts should have the same position
        resultWithStudentInTop10.topPlayers shouldHaveSize 10
        resultWithStudentInTop10.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 1.toUUID()
                firstName shouldBe "Alice"
                lastName shouldBe "1"
                city shouldBe "Prague"
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 1000.toBigDecimal()
                networkingVisibility shouldBe NetworkingVisibility.ENABLED
            }
            this[1].run {
                position shouldBe 2
                studentId shouldBe 2.toUUID()
                firstName shouldBe "Bob"
                lastName shouldBe "2"
                city shouldBe "Brno"
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 950.toBigDecimal()
                networkingVisibility shouldBe NetworkingVisibility.ENABLED
            }
            this[2].run {
                position shouldBe 3
                studentId shouldBe 3.toUUID()
                firstName shouldBe "Charlie"
                lastName shouldBe "3"
                city shouldBe "Ostrava"
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 800.toBigDecimal()
                networkingVisibility shouldBe NetworkingVisibility.ENABLED
            }
            this[3].run {
                position shouldBe 3
                studentId shouldBe 4.toUUID()
                firstName shouldBe "Delta"
                lastName shouldBe "4"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 800.toBigDecimal()
                networkingVisibility shouldBe NetworkingVisibility.ENABLED
            }
            this[4].run {
                position shouldBe 5
                studentId shouldBe 5.toUUID()
                firstName shouldBe "Edward"
                lastName shouldBe "5"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 750.toBigDecimal()
            }
            this[5].run {
                position shouldBe 6
                studentId shouldBe 6.toUUID()
                firstName shouldBe "Fiona"
                lastName shouldBe "6"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 700.toBigDecimal()
            }
            this[6].run {
                position shouldBe 7
                studentId shouldBe 7.toUUID()
                firstName shouldBe "George"
                lastName shouldBe "7"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 650.toBigDecimal()
            }
            this[7].run {
                position shouldBe 8
                studentId shouldBe 8.toUUID()
                firstName shouldBe "Hannah"
                lastName shouldBe "8"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 600.toBigDecimal()
            }
            this[8].run {
                position shouldBe 9
                studentId shouldBe 9.toUUID()
                firstName shouldBe "Ian"
                lastName shouldBe "9"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 550.toBigDecimal()
            }
            this[9].run {
                position shouldBe 10
                studentId shouldBe 10.toUUID()
                firstName shouldBe "Julia"
                lastName shouldBe "10"
                city shouldBe null
                profilePicture shouldBe null
                gameLevel shouldBe GameLevel.ONE
                gamePayout!! shouldBeEqualComparingTo 500.toBigDecimal()
            }
        }

        resultWithStudentInTop10.studentPlayer shouldNotBe null
        resultWithStudentInTop10.studentPlayer!!.run {
            position shouldBe 1
            studentId shouldBe 1.toUUID()
        }

        resultWithStudentOutsideTop10.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 1.toUUID()
            }
            this[1].run {
                position shouldBe 2
                studentId shouldBe 2.toUUID()
            }
            this[2].run {
                position shouldBe 3
                studentId shouldBe 3.toUUID()
            }
            this[3].run {
                position shouldBe 3
                studentId shouldBe 4.toUUID()
            }
            this[4].run {
                position shouldBe 5
                studentId shouldBe 5.toUUID()
            }
            this[5].run {
                position shouldBe 6
                studentId shouldBe 6.toUUID()
            }
            this[6].run {
                position shouldBe 7
                studentId shouldBe 7.toUUID()
            }
            this[7].run {
                position shouldBe 8
                studentId shouldBe 8.toUUID()
            }
            this[8].run {
                position shouldBe 9
                studentId shouldBe 9.toUUID()
            }
            this[9].run {
                position shouldBe 10
                studentId shouldBe 10.toUUID()
            }
        }
        resultWithStudentOutsideTop10.studentPlayer shouldNotBe null
        resultWithStudentOutsideTop10.studentPlayer!!.run {
            position shouldBe 15
            studentId shouldBe 15.toUUID()
        }
    }

    @Test
    fun `should return leaderboard with not enough students to fill all 10 places in the game`() {
        // Given
        // Create a game tournament for 2025
        dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 12, 31),
        )

        student(id = 1.toUUID(), first = "Student1", payout = 1000.00.toBigDecimal())
        student(id = 2.toUUID(), first = "Student2", payout = 800.00.toBigDecimal())
        student(id = 3.toUUID(), first = "Student3", payout = 600.00.toBigDecimal())

        // When
        val result = underTest.handle(
            GetGameLeaderboardQuery(
                userId = 1.toUUID(),
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        // Then
        result.topPlayers shouldHaveSize 3
        result.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 1.toUUID()
            }
            this[1].run {
                position shouldBe 2
                studentId shouldBe 2.toUUID()
            }
            this[2].run {
                position shouldBe 3
                studentId shouldBe 3.toUUID()
            }
        }
        result.studentPlayer shouldNotBe null
        result.studentPlayer!!.run {
            position shouldBe 1
            studentId shouldBe 1.toUUID()
        }
    }

    @Test
    fun `should return leaderboard when querying user is trader and admin`() {
        // Given
        // Create a game tournament for 2025
        dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 12, 31),
        )

        // Create 3 students with different payout amounts
        student(id = 1.toUUID(), first = "Student1", last = "Test1", payout = 1000.00.toBigDecimal())
        student(id = 2.toUUID(), first = "Student2", last = "Test2", payout = 800.00.toBigDecimal())
        student(id = 3.toUUID(), first = "Student3", last = "Test3", payout = 600.00.toBigDecimal())

        val trader = dataHelper.getAppUser(id = 100.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(id = it.id)
        }

        val admin = dataHelper.getAppUser(id = 101.toUUID(), userRole = UserRole.ADMIN)

        // When
        val traderResult = underTest.handle(
            GetGameLeaderboardQuery(
                userId = trader.id,
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        val adminResult = underTest.handle(
            GetGameLeaderboardQuery(
                userId = admin.id,
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        // Then
        // Verify that trader and admin users are not part of the game (studentPlayer is null)
        // But they should still see the top players in the leaderboard
        // This confirms that non-student users can view the leaderboard but aren't included in it
        traderResult.studentPlayer shouldBe null
        traderResult.topPlayers shouldHaveSize 3
        traderResult.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 1.toUUID()
            }
            this[1].run {
                position shouldBe 2
                studentId shouldBe 2.toUUID()
            }
            this[2].run {
                position shouldBe 3
                studentId shouldBe 3.toUUID()
            }
        }

        adminResult.studentPlayer shouldBe null
        adminResult.topPlayers shouldHaveSize 3
        adminResult.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 1.toUUID()
            }
            this[1].run {
                position shouldBe 2
                studentId shouldBe 2.toUUID()
            }
            this[2].run {
                position shouldBe 3
                studentId shouldBe 3.toUUID()
            }
        }
    }

    @Test
    fun `should return empty leaderboard when no approved documents exists`() {
        // Given
        // Create a game tournament for 2025
        dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 12, 31),
        )

        // The student has only awaiting and denied payout documents for 2025, but no approved ones
        val student = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Alice",
            lastName = "Wonderland",
            gameLevel = GameLevel.TWO,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "profile-url",
                        compressedFileUrl = "profile-url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
            dataHelper.getGameDocument(
                // awaiting document (not approved)
                studentId = it.id,
                type = GameDocumentType.PAYOUT,
                payoutAmount = 1000.00.toBigDecimal(),
                payoutDate = LocalDate.of(2025, 1, 1),
            )
            dataHelper.getGameDocument(
                // denied document (not approved)
                studentId = it.id,
                type = GameDocumentType.PAYOUT,
                payoutAmount = 1000.00.toBigDecimal(),
                payoutDate = LocalDate.of(2025, 1, 1),
                entityModifier = { it.denyAwaiting("Rejected") },
            )
        }

        // When
        val result = underTest.handle(
            GetGameLeaderboardQuery(
                userId = student.id,
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        // Then
        // student should still appear in studentPlayer but with null position and payout
        result.topPlayers shouldHaveSize 0
        result.studentPlayer shouldNotBe null
        result.studentPlayer!!.run {
            position shouldBe null
            studentId shouldBe student.id
            firstName shouldBe "Alice"
            lastName shouldBe "Wonderland"
            city shouldBe null
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "profile-url"
                imageCompressedUrl shouldBe "profile-url-comp"
                imageBlurHash shouldBe "123"
            }
            gameLevel shouldBe GameLevel.TWO
            gamePayout shouldBe null
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
        }
    }

    @Test
    fun `should return leaderboard only with payouts from queried year using PAYOUT_DATE`() {
        // Given
        // Create a game tournament for 2025
        dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 12, 31),
        )

        // Create students with approved payout documents for different years
        // Note: The leaderboard now uses PAYOUT_DATE instead of CREATED_AT for filtering by year
        student(
            id = 1.toUUID(),
            first = "Student1",
            last = "Test1",
            payout = 2000.00.toBigDecimal(),
            payoutDate = LocalDate.of(2024, 1, 1), // different payout year than query - will be excluded
            creationYear = 2025, // matches query year but not used for filtering
        )
        student(
            id = 2.toUUID(),
            first = "Student2",
            last = "Test2",
            payout = 1000.00.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 1, 1), // matches query year - will be included
            creationYear = 2025, // matches query year
        )
        student(
            id = 3.toUUID(),
            first = "Student3",
            last = "Test3",
            payout = 1000.00.toBigDecimal(),
            payoutDate = LocalDate.of(2024, 1, 1), // different payout year than query - will be excluded
            creationYear = 2024, // different creation year than the query
        )

        // When
        val result = underTest.handle(
            GetGameLeaderboardQuery(
                userId = 3.toUUID(),
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        // Then
        result.topPlayers shouldHaveSize 1
        result.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 2.toUUID()
                firstName shouldBe "Student2"
                lastName shouldBe "Test2"
            }
        }
        result.studentPlayer shouldNotBe null
        result.studentPlayer!!.run {
            position shouldBe null
            studentId shouldBe 3.toUUID()
            firstName shouldBe "Student3"
            lastName shouldBe "Test3"
        }
    }

    @Test
    fun `should filter leaderboard by PAYOUT_DATE not CREATED_AT when years differ`() {
        // Given
        // Create a game tournament for 2025
        dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 12, 31),
        )

        // Create students with same creation year but different payout years
        // This test specifically verifies that PAYOUT_DATE is used for filtering, not CREATED_AT

        // Student with payout in 2025 but created in 2024 - should be included in 2025 results
        student(
            id = 1.toUUID(),
            first = "Student1",
            last = "Test1",
            payout = 2000.00.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 1, 1), // matches query year - will be included
            creationYear = 2024, // different from query year
        )

        // Student with payout in 2024 but created in 2025 - should NOT be included in 2025 results
        student(
            id = 2.toUUID(),
            first = "Student2",
            last = "Test2",
            payout = 3000.00.toBigDecimal(),
            payoutDate = LocalDate.of(2024, 1, 1), // different from query year - will be excluded
            creationYear = 2025, // matches query year but not used for filtering
        )

        // When
        val result = underTest.handle(
            GetGameLeaderboardQuery(
                userId = 2.toUUID(),
                today = LocalDate.of(2025, 6, 15),
            ),
        )

        // Then
        // Only Student1 should be in the results because filtering is by PAYOUT_DATE
        result.topPlayers shouldHaveSize 1
        result.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 1.toUUID()
                firstName shouldBe "Student1"
                lastName shouldBe "Test1"
                gamePayout!! shouldBeEqualComparingTo 2000.00.toBigDecimal()
            }
        }

        // Student2 should be the current user but with no position (not in top players)
        result.studentPlayer shouldNotBe null
        result.studentPlayer!!.run {
            position shouldBe null
            studentId shouldBe 2.toUUID()
            firstName shouldBe "Student2"
            lastName shouldBe "Test2"
            gamePayout shouldBe null // No payout for 2025
        }
    }

    @Test
    fun `should return leaderboard with payouts only from the queried tournament when multiple tournaments exist`() {
        // Given
        // Create first tournament for Q1 2025 (January - March)
        val firstTournament = dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 3, 31),
        )

        // Create second tournament for Q2 2025 (April - June)
        val secondTournament = dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 4, 1),
            finishAt = LocalDate.of(2025, 6, 30),
        )

        // Create students with payouts in the first tournament (Q1)
        student(
            id = 1.toUUID(),
            first = "Alice",
            last = "FirstTournament",
            payout = 1000.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 2, 15), // Q1 tournament
        )
        student(
            id = 2.toUUID(),
            first = "Bob",
            last = "FirstTournament",
            payout = 800.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 3, 10), // Q1 tournament
        )

        // Create students with payouts in the second tournament (Q2)
        student(
            id = 3.toUUID(),
            first = "Charlie",
            last = "SecondTournament",
            payout = 1200.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 5, 15), // Q2 tournament
        )
        student(
            id = 4.toUUID(),
            first = "Delta",
            last = "SecondTournament",
            payout = 900.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 4, 20), // Q2 tournament
        )

        // Create a student with payouts in both tournaments
        student(
            id = 5.toUUID(),
            first = "Echo",
            last = "BothTournaments",
            payout = 500.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 2, 1), // Q1 tournament
        )
        // Add another payout for the same student in Q2
        dataHelper.getGameDocument(
            studentId = 5.toUUID(),
            type = GameDocumentType.PAYOUT,
            payoutAmount = 700.toBigDecimal(),
            payoutDate = LocalDate.of(2025, 5, 1), // Q2 tournament
            entityModifier = { it.approveAwaiting() },
        )

        // When - Query leaderboard for the second tournament (Q2)
        val result = underTest.handle(
            GetGameLeaderboardQuery(
                userId = 3.toUUID(),
                today = LocalDate.of(2025, 5, 15), // During Q2 tournament
            ),
        )

        // Then
        // Should only include payouts from the second tournament (Q2)
        // Expected ranking: Charlie (1200), Delta (900), Echo (700)
        // Alice and Bob should NOT appear as their payouts are from Q1
        result.topPlayers shouldHaveSize 3
        result.topPlayers.run {
            this[0].run {
                position shouldBe 1
                studentId shouldBe 3.toUUID()
                firstName shouldBe "Charlie"
                lastName shouldBe "SecondTournament"
                gamePayout!! shouldBeEqualComparingTo 1200.toBigDecimal()
            }
            this[1].run {
                position shouldBe 2
                studentId shouldBe 4.toUUID()
                firstName shouldBe "Delta"
                lastName shouldBe "SecondTournament"
                gamePayout!! shouldBeEqualComparingTo 900.toBigDecimal()
            }
            this[2].run {
                position shouldBe 3
                studentId shouldBe 5.toUUID()
                firstName shouldBe "Echo"
                lastName shouldBe "BothTournaments"
                gamePayout!! shouldBeEqualComparingTo 700.toBigDecimal() // Only Q2 payout
            }
        }

        // Verify the queried student (Charlie) is correctly positioned
        result.studentPlayer shouldNotBe null
        result.studentPlayer!!.run {
            position shouldBe 1
            studentId shouldBe 3.toUUID()
            firstName shouldBe "Charlie"
            lastName shouldBe "SecondTournament"
            gamePayout!! shouldBeEqualComparingTo 1200.toBigDecimal()
        }
    }

    private fun student(
        id: UUID,
        first: String,
        last: String = "Doe",
        payout: BigDecimal,
        city: String? = null,
        payoutDate: LocalDate = LocalDate.of(2025, 1, 1),
        creationYear: Int = 2025,
        networkingVisibility: NetworkingVisibility = NetworkingVisibility.ENABLED,
    ): Student = dataHelper.getStudent(
        id = id,
        firstName = first,
        lastName = last,
        gameLevel = GameLevel.ONE,
        locationId = city?.let { dataHelper.getUserLocation(city = it).id },
        networkingVisibility = networkingVisibility,
    ).also { student ->
        dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = payout,
            payoutDate = payoutDate,
            createdTimestamp = LocalDate.of(creationYear, 1, 1).toStartOfTheDay(),
            entityModifier = { it.approveAwaiting() },
        )
    }
}
