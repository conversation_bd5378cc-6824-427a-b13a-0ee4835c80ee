package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.command.LessonAttachmentInput
import com.cleevio.fundedmind.application.module.lesson.command.UpdateLessonCommand
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFoundException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotRelatedToCourseException
import com.cleevio.fundedmind.domain.file.AppFileRepository
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentRepository
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotRelatedToModuleException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class UpdateLessonCommandHandlerTest @Autowired constructor(
    private val underTest: UpdateLessonCommandHandler,
    private val lessonRepository: LessonRepository,
    private val lessonAttachmentRepository: LessonAttachmentRepository,
    private val appFileRepository: AppFileRepository,
) : IntegrationTest() {

    @Test
    fun `should update lesson with new values and attachments`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(
            id = 1.toUUID(),
            listingOrder = 1,
            courseModuleId = courseModule.id,
            title = "Old Title",
            description = "Old Description",
            videoUrl = "old-video-url",
            durationInSeconds = 1800,
        ).also { lesson ->
            // this attachment will be deleted
            dataHelper.getLessonAttachment(
                id = 1.toUUID(),
                lessonId = lesson.id,
                displayOrder = 1,
                name = "old-attachment-1",
                type = LessonAttachmentType.PDF,
                entityModifier = { it.changeAttachmentDocument(dataHelper.getDocument(id = 10.toUUID()).id) },
            )
            // this attachment will be updated
            dataHelper.getLessonAttachment(
                id = 2.toUUID(),
                lessonId = lesson.id,
                displayOrder = 2,
                name = "old-attachment-2",
                type = LessonAttachmentType.JPG,
                entityModifier = { it.changeAttachmentDocument(dataHelper.getDocument(id = 20.toUUID()).id) },
            )
        }

        // when
        underTest.handle(
            defaultCommand(
                courseId = course.id,
                courseModuleId = courseModule.id,
                lessonId = lesson.id,
                title = "New Lesson",
                description = "New Description",
                videoUrl = "video-url",
                durationInSeconds = 7200,
                thumbnailUrl = "new-thumbnail-url",
                thumbnailAnimationUrl = "new-thumbnail-animation-url",
                attachments = listOf(
                    // update existing attachment with new name and type
                    LessonAttachmentInput(
                        lessonAttachmentId = 2.toUUID(),
                        name = "old-attachment-2-new-name",
                        type = LessonAttachmentType.PNG,
                    ),
                    // create new attachment
                    LessonAttachmentInput(
                        lessonAttachmentId = null,
                        name = "new-attachment",
                        type = LessonAttachmentType.ZIP,
                    ),
                ),
            ),
        )

        // then
        lessonRepository.findByIdOrNull(lesson.id)!!.run {
            listingOrder shouldBe 1
            title shouldBe "New Lesson"
            description shouldBe "New Description"
            videoUrl shouldBe "video-url"
            durationInSeconds shouldBe 7200
            thumbnailUrl shouldBe "new-thumbnail-url"
            thumbnailAnimationUrl shouldBe "new-thumbnail-animation-url"
        }

        lessonAttachmentRepository.findAll().run {
            size shouldBe 2
            first { it.nameWithExtension == "old-attachment-2-new-name" }.run {
                id shouldBe 2.toUUID()
                displayOrder shouldBe 1
                type shouldBe LessonAttachmentType.PNG
                lessonId shouldBe lesson.id
            }
            first { it.nameWithExtension == "new-attachment" }.run {
                id shouldNotBe 2.toUUID()
                id shouldNotBe 1.toUUID()
                displayOrder shouldBe 2
                type shouldBe LessonAttachmentType.ZIP
                lessonId shouldBe lesson.id
            }
        }

        // file of deleted attachment should be deleted, file of updated attachment should remain
        appFileRepository.findAll().run {
            size shouldBe 1
            single().id shouldBe 20.toUUID()
        }
    }

    @Test
    fun `should throw if course module does not exist`() {
        shouldThrow<CourseModuleNotFoundException> {
            underTest.handle(
                defaultCommand(
                    courseId = 1.toUUID(),
                    courseModuleId = 999.toUUID(),
                    lessonId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if lesson does not exist`() {
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                defaultCommand(
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonId = 999.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if lesson is not related to course module`() {
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule1 = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val differentModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule1.id)

        shouldThrow<LessonNotRelatedToModuleException> {
            underTest.handle(
                defaultCommand(
                    courseId = course.id,
                    courseModuleId = differentModule.id,
                    lessonId = lesson.id,
                ),
            )
        }
    }

    @Test
    fun `should throw if course module is not related to course`() {
        val course1 = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val differentCourse = dataHelper.getCourse(id = 2.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course1.id)
        val lesson = dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id)

        shouldThrow<CourseModuleNotRelatedToCourseException> {
            underTest.handle(
                defaultCommand(
                    courseId = differentCourse.id,
                    courseModuleId = courseModule.id,
                    lessonId = lesson.id,
                ),
            )
        }
    }

    private fun defaultCommand(
        courseId: UUID,
        courseModuleId: UUID,
        lessonId: UUID,
        title: String = "New Lesson",
        description: String = "New Description",
        videoUrl: String = "video-url",
        durationInSeconds: Int = 7200,
        thumbnailUrl: String = "thumbnail-url",
        thumbnailAnimationUrl: String = "thumbnail-animation-url",
        attachments: List<LessonAttachmentInput> = emptyList(),
    ) = UpdateLessonCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        lessonId = lessonId,
        title = title,
        description = description,
        videoUrl = videoUrl,
        durationInSeconds = durationInSeconds,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        attachments = attachments,
    )
}
