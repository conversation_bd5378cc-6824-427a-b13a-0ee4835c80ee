package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.module.user.student.command.StudentPatchesLocationCommand
import com.cleevio.fundedmind.domain.location.UserLocationRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class StudentPatchesLocationCommandHandlerTest @Autowired constructor(
    private val underTest: StudentPatchesLocationCommandHandler,
    private val studentRepository: StudentRepository,
    private val userLocationRepository: UserLocationRepository,
) : IntegrationTest() {

    @Test
    fun `should add new location to student`() {
        // Create a student without location
        dataHelper.getStudent(id = 1.toUUID(), locationId = null).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT, hubspotIdentifier = 1)
        }

        // Create a location input
        val locationInput = UserLocationInput(
            street = "Street 123",
            city = "City",
            postalCode = "12345",
            state = "Country",
            geoLocation = GeoLocationInput(
                latitude = 49.1950610,
                longitude = 16.606836,
            ),
        )

        every { hubspotService.updateCrmUserLocation(any(), any()) } just Runs

        // Execute the command
        underTest.handle(
            StudentPatchesLocationCommand(
                studentId = 1.toUUID(),
                location = locationInput,
            ),
        )

        // Verify student has a location now
        val student = studentRepository.findByIdOrNull(1.toUUID())!!
        student.locationId shouldNotBe null

        // Verify location data
        userLocationRepository.findByIdOrNull(student.locationId!!)!!.run {
            street shouldBe "Street 123"
            city shouldBe "City"
            postalCode shouldBe "12345"
            state shouldBe "Country"
            latitude shouldBe 49.1950610
            longitude shouldBe 16.606836

            // Verify that obfuscated values are different from original values
            obfuscatedLatitude shouldNotBe latitude
            obfuscatedLongitude shouldNotBe longitude
        }

        verify {
            hubspotService.updateCrmUserLocation(1, "Street 123, 12345 City, Country")
        }
    }

    @Test
    fun `should update existing location`() {
        // Create a location
        val existingLocation = dataHelper.getUserLocation(
            id = 1.toUUID(),
            street = "Old Street 123",
            city = "Old City",
            postalCode = "54321",
            state = "Old Country",
            latitude = 48.1950610,
            longitude = 17.606836,
        )

        // Create a student with the location
        dataHelper.getStudent(
            id = 1.toUUID(),
            locationId = existingLocation.id,
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT, hubspotIdentifier = 1)
        }

        // Create a new location input
        val newLocationInput = UserLocationInput(
            street = "New Street 456",
            city = "New City",
            postalCode = "12345",
            state = "New Country",
            geoLocation = GeoLocationInput(
                latitude = 49.1950610,
                longitude = 16.606836,
            ),
        )

        every { hubspotService.updateCrmUserLocation(1, any()) } just Runs

        // Execute the command
        underTest.handle(
            StudentPatchesLocationCommand(
                studentId = 1.toUUID(),
                location = newLocationInput,
            ),
        )

        // Verify student has a different location now
        val student = studentRepository.findByIdOrNull(1.toUUID())!!
        student.locationId shouldNotBe null
        student.locationId shouldNotBe existingLocation.id

        // Verify old location is deleted
        userLocationRepository.findByIdOrNull(existingLocation.id) shouldBe null

        // Verify new location data
        userLocationRepository.findByIdOrNull(student.locationId!!)!!.run {
            street shouldBe "New Street 456"
            city shouldBe "New City"
            postalCode shouldBe "12345"
            state shouldBe "New Country"
            latitude shouldBe 49.1950610
            longitude shouldBe 16.606836
        }

        verify {
            hubspotService.updateCrmUserLocation(1, "New Street 456, 12345 New City, New Country")
        }
    }

    @Test
    fun `should remove location from student`() {
        // Create a location
        val existingLocation = dataHelper.getUserLocation(
            id = 1.toUUID(),
            street = "Old Street 123",
            city = "Old City",
            postalCode = "54321",
            state = "Old Country",
            latitude = 48.1950610,
            longitude = 17.606836,
        )

        // Create a student with the location
        dataHelper.getStudent(
            id = 1.toUUID(),
            locationId = existingLocation.id,
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT, hubspotIdentifier = 1)
        }

        every { hubspotService.updateCrmUserLocation(any(), any()) } just Runs

        // Execute the command with null location
        underTest.handle(
            StudentPatchesLocationCommand(
                studentId = 1.toUUID(),
                location = null,
            ),
        )

        // Verify student has no location now
        val student = studentRepository.findByIdOrNull(1.toUUID())!!
        student.locationId shouldBe null

        // Verify old location is deleted
        userLocationRepository.findByIdOrNull(existingLocation.id) shouldBe null

        verify {
            hubspotService.updateCrmUserLocation(1, null)
        }
    }
}
