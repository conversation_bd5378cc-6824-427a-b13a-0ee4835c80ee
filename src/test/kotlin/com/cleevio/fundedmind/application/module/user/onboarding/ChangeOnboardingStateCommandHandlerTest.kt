package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.onboarding.command.ChangeOnboardingStateCommand
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ChangeOnboardingStateCommandHandlerTest @Autowired constructor(
    private val underTest: ChangeOnboardingStateCommandHandler,
    private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `should change onboarding state`() {
        dataHelper.getStudentForOnboarding(1.toUUID())

        underTest.handle(
            ChangeOnboardingStateCommand(
                studentId = 1.toUUID(),
                newState = OnboardingState.MASTERCLASS_CHECKOUT,
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.onboardingState shouldBe OnboardingState.MASTERCLASS_CHECKOUT
    }

//    @Test
//    fun `should throw if student already onboarded`() {
//        dataHelper.getStudentForOnboarding(
//            id = 1.toUUID(),
//            entityModifier = { it.changeState(newState = OnboardingState.ONBOARDING_COMPLETE) },
//        )
//
//        shouldThrow<StudentAlreadyOnboardedException> {
//            underTest.handle(
//                ChangeOnboardingStateCommand(
//                    studentId = 1.toUUID(),
//                    newState = OnboardingState.MASTERCLASS_CHECKOUT,
//                ),
//            )
//        }
//    }
}
