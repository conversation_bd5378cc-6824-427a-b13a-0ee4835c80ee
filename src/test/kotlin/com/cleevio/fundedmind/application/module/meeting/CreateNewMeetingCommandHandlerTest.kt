package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.command.CreateNewMeetingCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.meeting.MeetingRepository
import com.cleevio.fundedmind.domain.meeting.exception.MeetingTimeIsIncorrectException
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.TraderInMeetingRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.Instant
import java.util.UUID

class CreateNewMeetingCommandHandlerTest @Autowired constructor(
    private val underTest: CreateNewMeetingCommandHandler,
    private val meetingRepository: MeetingRepository,
    private val traderInMeetingRepository: TraderInMeetingRepository,
) : IntegrationTest() {

    @Test
    fun `should create new meeting with traders in given order`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        val result = underTest.handle(
            CreateNewMeetingCommand(
                name = "Meeting 1",
                color = Color.BLUE,
                startAt = "2025-01-01T10:00:00Z".toInstant(),
                finishAt = "2025-01-01T12:00:00Z".toInstant(),
                traderIds = listOf(2.toUUID(), 1.toUUID()),
                description = "Meeting description",
                invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
                invitedDiscordUsers = true,
                meetingUrl = "meeting-url",
                recordingUrl = "recording-url",
            ),
        )

        val meeting = meetingRepository.findByIdOrNull(result.id)!!

        meeting.run {
            name shouldBe "Meeting 1"
            color shouldBe Color.BLUE
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            description shouldBe "Meeting description"
            invitedTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            invitedDiscordUsers shouldBe true
            meetingUrl shouldBe "meeting-url"
            recordingUrl shouldBe "recording-url"
            coverPhotoFileId shouldBe null
        }

        val tradersInMeeting = traderInMeetingRepository.findAll()
        tradersInMeeting shouldHaveSize 2
        tradersInMeeting.first { it.traderId == 1.toUUID() }.run {
            meetingId shouldBe result.id
            displayOrder shouldBe 2
        }
        tradersInMeeting.first { it.traderId == 2.toUUID() }.run {
            meetingId shouldBe result.id
            displayOrder shouldBe 1
        }
    }

    @Test
    fun `should throw if start time is after finish time`() {
        shouldThrow<MeetingTimeIsIncorrectException> {
            underTest.handle(
                defaultCommand(
                    startAt = "2025-01-01T12:00:00Z".toInstant(),
                    finishAt = "2025-01-01T10:00:00Z".toInstant(),
                ),
            )
        }
    }

    @Test
    fun `should throw if trader does not exist`() {
        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                defaultCommand(
                    traderIds = listOf(1.toUUID()),
                ),
            )
        }
    }

    private fun defaultCommand(
        name: String = "Meeting 1",
        color: Color = Color.BLUE,
        startAt: Instant = "2025-01-01T10:00:00Z".toInstant(),
        finishAt: Instant = "2025-01-01T12:00:00Z".toInstant(),
        description: String = "Meeting description",
        invitedTiers: List<StudentTier> = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
        invitedDiscordUsers: Boolean = true,
        meetingUrl: String = "meeting-url",
        recordingUrl: String = "recording-url",
        traderIds: List<UUID> = listOf(),
    ) = CreateNewMeetingCommand(
        name = name,
        color = color,
        startAt = startAt,
        finishAt = finishAt,
        traderIds = traderIds,
        description = description,
        invitedTiers = invitedTiers,
        invitedDiscordUsers = invitedDiscordUsers,
        meetingUrl = meetingUrl,
        recordingUrl = recordingUrl,
    )
}
