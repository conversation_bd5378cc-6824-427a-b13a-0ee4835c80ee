package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.appuser.query.AutocompleteStudentsQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.parseIntegerList
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class AutocompleteStudentUserQueryHandlerTest @Autowired constructor(
    private val underTest: AutocompleteStudentsQueryHandler,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "null; [1,2,3,4,5]",
            "abc;  [1,2,3]",
            "xyz;  [4,5,6]",
            "7;    [7]",
            "10;   [ ]",
            "com;  [1,2,3,4,5]",
        ],
    )
    fun `should return filtered and sorted users based on searchString`(
        searchString: String?,
        expectedIdsRaw: String,
    ) {
        // given
        val expectedIds: List<UUID> = expectedIdsRaw.parseIntegerList().map { it.toUUID() }

        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 1.toUUID())
        }
        dataHelper.getAppUser(id = 2.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 2.toUUID())
        }
        dataHelper.getAppUser(id = 3.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 3.toUUID())
        }
        dataHelper.getAppUser(id = 4.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 4.toUUID())
        }
        dataHelper.getAppUser(id = 5.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 5.toUUID())
        }
        dataHelper.getAppUser(id = 6.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 6.toUUID())
        }
        dataHelper.getAppUser(id = 7.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 7.toUUID())
        }
        dataHelper.getAppUser(id = 8.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 8.toUUID())
        }
        dataHelper.getAppUser(id = 9.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 9.toUUID())
        }

        // when
        val result: AutocompleteStudentsQuery.Result = underTest.handle(
            defaultQuery(searchString = searchString, limit = 5),
        )

        // then
        result.data.map { it.userId } shouldBe expectedIds
    }

    @Test
    fun `should return empty result if no users found`() {
        val result = underTest.handle(
            defaultQuery(searchString = null),
        )

        result.data shouldBe emptyList()
    }

    @Test
    fun `should return only users in STUDENT role and with finished onboarding`() {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = 3.toUUID())
        }
        dataHelper.getAppUser(id = 4.toUUID(), userRole = UserRole.STUDENT)

        val result = underTest.handle(
            defaultQuery(searchString = null),
        )

        result.data.map { it.userId } shouldBe listOf(3.toUUID())
    }

    @Test
    fun `should return only the student with the specified studentId`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 1.toUUID())
        }
        dataHelper.getAppUser(id = 2.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 2.toUUID())
        }
        dataHelper.getAppUser(id = 3.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 3.toUUID())
        }

        // when
        val result = underTest.handle(
            defaultQuery(studentId = 2.toUUID(), searchString = "abc"),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().userId shouldBe 2.toUUID()
    }

    @Test
    fun `should return with name and profile picture`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
        )

        dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "First",
            lastName = "Last",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            defaultQuery(searchString = null),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            firstName shouldBe "First"
            lastName shouldBe "Last"
            email shouldBe "<EMAIL>"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "123"
            }
        }
    }

    @Test
    fun `should filter students by student tiers`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)
        }
        dataHelper.getAppUser(id = 2.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 2.toUUID(), studentTier = StudentTier.MASTERCLASS)
        }
        dataHelper.getAppUser(id = 3.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 3.toUUID(), studentTier = StudentTier.EXCLUSIVE)
        }
        dataHelper.getAppUser(id = 4.toUUID(), email = "<EMAIL>").also {
            dataHelper.getStudent(id = 4.toUUID(), studentTier = StudentTier.NO_TIER)
        }

        // when - filter by MASTERCLASS and EXCLUSIVE
        val result1 = underTest.handle(
            defaultQuery(studentTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)),
        )

        // then
        result1.data shouldHaveSize 2
        result1.data.map { it.userId }.toSet() shouldBe setOf(2.toUUID(), 3.toUUID())

        // when - filter by EXCLUSIVE only
        val result2 = underTest.handle(
            defaultQuery(studentTiers = listOf(StudentTier.EXCLUSIVE)),
        )

        // then
        result2.data shouldHaveSize 1
        result2.data.single().userId shouldBe 3.toUUID()

        // when - filter by NO_TIER only
        val result3 = underTest.handle(
            defaultQuery(studentTiers = listOf(StudentTier.NO_TIER)),
        )

        // then
        result3.data shouldHaveSize 1
        result3.data.single().userId shouldBe 4.toUUID()
    }

    @Test
    fun `should search by first name, last name, concatenated name, and email`() {
        // given
        // Create users with different names and emails
        val joeSmith = dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(
                id = 1.toUUID(),
                firstName = "Joe",
                lastName = "Smith",
            )
        }

        val joeDoe = dataHelper.getAppUser(
            id = 2.toUUID(),
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(
                id = 2.toUUID(),
                firstName = "Joe",
                lastName = "Doe",
            )
        }

        val janeDoe = dataHelper.getAppUser(
            id = 3.toUUID(),
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(
                id = 3.toUUID(),
                firstName = "Jane",
                lastName = "Doe",
            )
        }

        val bobJohnson = dataHelper.getAppUser(
            id = 4.toUUID(),
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(
                id = 4.toUUID(),
                firstName = "Bob",
                lastName = "Johnson",
            )
        }

        // When first name
        val firstNameResult = underTest.handle(
            defaultQuery(searchString = "joe"),
        )

        // Then first name
        firstNameResult.data shouldHaveSize 2
        firstNameResult.data.map { it.userId }.toSet() shouldBe setOf(joeSmith.id, joeDoe.id)

        // When last name
        val lastNameResult = underTest.handle(
            defaultQuery(searchString = "doe"),
        )

        // Then last name
        lastNameResult.data shouldHaveSize 2
        lastNameResult.data.map { it.userId }.toSet() shouldBe setOf(joeDoe.id, janeDoe.id)

        // When concatenated
        val concatenatedNameResult = underTest.handle(
            defaultQuery(searchString = "joe doe"),
        )

        // Then concatenated
        concatenatedNameResult.data shouldHaveSize 1
        concatenatedNameResult.data.single().userId shouldBe joeDoe.id

        // When email
        val emailResult = underTest.handle(
            defaultQuery(searchString = "jane.doe"),
        )

        // Then email
        emailResult.data shouldHaveSize 1
        emailResult.data.single().userId shouldBe janeDoe.id
    }

    private fun defaultQuery(
        limit: Int = 5,
        searchString: String? = null,
        studentId: UUID? = null,
        studentTiers: List<StudentTier>? = null,
    ) = AutocompleteStudentsQuery(
        limit = limit,
        filter = AutocompleteStudentsQuery.Filter(
            searchString = searchString,
            studentId = studentId,
            studentTiers = studentTiers,
        ),
    )
}
