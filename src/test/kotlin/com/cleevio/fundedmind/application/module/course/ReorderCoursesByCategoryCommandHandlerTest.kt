package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.command.ReorderCoursesByCategoryCommand
import com.cleevio.fundedmind.application.module.course.command.ReorderCoursesByCategoryCommand.CourseOrderingInput
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.course.exception.ActiveCoursesMismatchException
import com.cleevio.fundedmind.domain.course.exception.CourseNotFoundException
import com.cleevio.fundedmind.domain.course.exception.CourseOrderCannotBeNegativeException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReorderCoursesByCategoryCommandHandlerTest @Autowired constructor(
    private val underTest: ReorderCoursesByCategoryCommandHandler,
    private val courseRepository: CourseRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder courses`() {
        dataHelper.getTrader(id = 1.toUUID())

        dataHelper.getCourse(id = 1.toUUID(), listingOrder = 1, traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), listingOrder = 2, traderId = 1.toUUID())
        dataHelper.getCourse(id = 3.toUUID(), listingOrder = 3, traderId = 1.toUUID())

        dataHelper.getCourse(
            id = 4.toUUID(),
            listingOrder = 4,
            traderId = 1.toUUID(),
            entityModifier = { it.softDelete() },
        )

        underTest.handle(
            ReorderCoursesByCategoryCommand(
                courseCategory = CourseCategory.TRADING_BASICS,
                courseOrderings = listOf(
                    CourseOrderingInput(courseId = 1.toUUID(), newListingOrder = 2),
                    CourseOrderingInput(courseId = 2.toUUID(), newListingOrder = 3),
                    CourseOrderingInput(courseId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        val courses = courseRepository.findAll()
        courses shouldHaveSize 4
        courses.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        courses.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        courses.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
        courses.first { it.id == 4.toUUID() }.listingOrder shouldBe 4 // not changed
    }

    @Test
    fun `should reorder courses by category only in the given category`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            listingOrder = 2,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
        )
        // different category
        dataHelper.getCourse(
            id = 3.toUUID(),
            listingOrder = 9,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE,
        )

        // when
        underTest.handle(
            ReorderCoursesByCategoryCommand(
                courseCategory = CourseCategory.BASECAMP,
                courseOrderings = listOf(
                    CourseOrderingInput(courseId = 1.toUUID(), newListingOrder = 2),
                    CourseOrderingInput(courseId = 2.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val courses = courseRepository.findAll()
        courses shouldHaveSize 3
        courses.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        courses.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        courses.first { it.id == 3.toUUID() }.listingOrder shouldBe 9
    }

    @Test
    fun `should throw course count by category does not match`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE,
        )
        dataHelper.getCourse(
            id = 3.toUUID(),
            listingOrder = 2,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE,
        )

        shouldThrow<ActiveCoursesMismatchException> {
            underTest.handle(
                ReorderCoursesByCategoryCommand(
                    courseCategory = CourseCategory.EXCLUSIVE,
                    // count of course orderings is 1 and expected to match amount of EXCLUSIVE courses - 2 courses
                    courseOrderings = listOf(
                        CourseOrderingInput(courseId = 1.toUUID(), newListingOrder = 2),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw course count by category match but course is of different category`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            listingOrder = 2,
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE,
        )

        shouldThrow<CourseNotFoundException> {
            underTest.handle(
                ReorderCoursesByCategoryCommand(
                    courseCategory = CourseCategory.BASECAMP,
                    // count of course orderings is 1 and matches amount of base camp courses
                    // however provided course is not BASECAMP
                    courseOrderings = listOf(
                        CourseOrderingInput(courseId = 2.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should reorder courses even if display order is not unique`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())

        dataHelper.getCourse(id = 1.toUUID(), listingOrder = 1, traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), listingOrder = 2, traderId = 1.toUUID())
        dataHelper.getCourse(id = 3.toUUID(), listingOrder = 3, traderId = 1.toUUID())

        // when
        underTest.handle(
            ReorderCoursesByCategoryCommand(
                courseCategory = CourseCategory.TRADING_BASICS,
                courseOrderings = listOf(
                    CourseOrderingInput(courseId = 1.toUUID(), newListingOrder = 1),
                    CourseOrderingInput(courseId = 2.toUUID(), newListingOrder = 1),
                    CourseOrderingInput(courseId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val courses = courseRepository.findAll()
        courses shouldHaveSize 3
        courses.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        courses.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        courses.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of courses - course is missing`() {
        dataHelper.getTrader(id = 1.toUUID())

        dataHelper.getCourse(id = 1.toUUID(), listingOrder = 1, traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), listingOrder = 2, traderId = 1.toUUID())
        dataHelper.getCourse(id = 3.toUUID(), listingOrder = 3, traderId = 1.toUUID())

        shouldThrow<ActiveCoursesMismatchException> {
            underTest.handle(
                ReorderCoursesByCategoryCommand(
                    courseCategory = CourseCategory.TRADING_BASICS,
                    courseOrderings = listOf(
                        CourseOrderingInput(courseId = 1.toUUID(), newListingOrder = 2),
                        CourseOrderingInput(courseId = 3.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if display order is not positive or zero`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(id = 1.toUUID(), listingOrder = 1, traderId = 1.toUUID())

        shouldThrow<CourseOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderCoursesByCategoryCommand(
                    courseCategory = CourseCategory.TRADING_BASICS,
                    courseOrderings = listOf(
                        CourseOrderingInput(courseId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}
