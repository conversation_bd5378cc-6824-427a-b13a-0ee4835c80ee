package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.query.GetLessonDetailQuery
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFoundException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotRelatedToCourseException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetLessonDetailQueryHandlerTest @Autowired constructor(
    private val underTest: GetLessonDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get lesson detail with all fields and attachments`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        val lesson = dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            title = "Lesson Title",
            description = "Lesson Description",
            videoUrl = "video-url",
            durationInSeconds = 3600,
            thumbnailUrl = "thumbnail-url",
            thumbnailAnimationUrl = "thumbnail-animation-url",
            listingOrder = 1,
        ).also { lesson ->
            dataHelper.getLessonAttachment(
                id = 111.toUUID(),
                lessonId = lesson.id,
                displayOrder = 1,
                name = "PDF Document",
                type = LessonAttachmentType.PDF,
                entityModifier = {
                    it.changeAttachmentDocument(
                        dataHelper.getDocument(
                            id = 1110.toUUID(),
                            type = FileType.LESSON_ATTACHMENT,
                        ).id,
                    )
                },
            )
            dataHelper.getLessonAttachment(
                id = 112.toUUID(),
                lessonId = lesson.id,
                displayOrder = 2,
                name = "Excel Sheet",
                type = LessonAttachmentType.XLSX,
                entityModifier = {
                    it.changeAttachmentDocument(
                        dataHelper.getDocument(
                            id = 1120.toUUID(),
                            type = FileType.LESSON_ATTACHMENT,
                        ).id,
                    )
                },
            )
        }

        // when
        val result = underTest.handle(
            GetLessonDetailQuery(
                courseId = course.id,
                courseModuleId = courseModule.id,
                lessonId = lesson.id,
            ),
        )

        // then
        result.run {
            lessonId shouldBe lesson.id
            courseModuleId shouldBe courseModule.id
            title shouldBe "Lesson Title"
            description shouldBe "Lesson Description"
            videoUrl shouldBe "video-url"
            durationInSeconds shouldBe 3600
            thumbnailUrl shouldBe "thumbnail-url"
            thumbnailAnimationUrl shouldBe "thumbnail-animation-url"
            attachments shouldHaveSize 2
            attachments.first { it.nameWithExtension == "PDF Document" }.run {
                displayOrder shouldBe 1
                document shouldNotBe null
                document!!.documentId shouldBe 1110.toUUID()

                type shouldBe LessonAttachmentType.PDF
            }
            attachments.first { it.nameWithExtension == "Excel Sheet" }.run {
                displayOrder shouldBe 2
                document shouldNotBe null
                document!!.documentId shouldBe 1120.toUUID()
                type shouldBe LessonAttachmentType.XLSX
            }
        }
    }

    @Test
    fun `should get lesson detail without attachments`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            title = "Simple Lesson",
            description = "Simple Description",
            videoUrl = "video-url",
            durationInSeconds = 1800,
        )

        // when
        val result = underTest.handle(
            GetLessonDetailQuery(
                courseId = course.id,
                courseModuleId = courseModule.id,
                lessonId = lesson.id,
            ),
        )

        // then
        result.attachments shouldHaveSize 0
    }

    @Test
    fun `should throw if course module does not exist`() {
        shouldThrow<CourseModuleNotFoundException> {
            underTest.handle(
                GetLessonDetailQuery(
                    courseId = 1.toUUID(),
                    courseModuleId = 999.toUUID(),
                    lessonId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if course module is not related to course`() {
        val course1 = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val course2 = dataHelper.getCourse(id = 2.toUUID(), traderId = dataHelper.getTrader(2.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course1.id)

        shouldThrow<CourseModuleNotRelatedToCourseException> {
            underTest.handle(
                GetLessonDetailQuery(
                    courseId = course2.id,
                    courseModuleId = courseModule.id,
                    lessonId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if lesson does not exist`() {
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                GetLessonDetailQuery(
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonId = 999.toUUID(),
                ),
            )
        }
    }
}
