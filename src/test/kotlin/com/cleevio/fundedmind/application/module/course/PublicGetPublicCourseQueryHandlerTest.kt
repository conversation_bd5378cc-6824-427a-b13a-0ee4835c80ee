package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.PublicGetPublicCourseQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class PublicGetPublicCourseQueryHandlerTest @Autowired constructor(
    private val underTest: PublicGetPublicCourseQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get public course - verify mappings`() {
        // given
        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            title = "Course",
            description = "Description",
            trailerUrl = "trailer-url",
            color = Color.ORANGE,
            courseCategory = CourseCategory.TRADING_BASICS,
            homepage = true,
            public = true,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(
                courseId = course.id,
                title = "Module 1",
                description = "Module 1 Description",
                listingOrder = 1,
            ).also { module1 ->
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                )
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                )
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                    entityModifier = { it.softDelete() },
                )
            }
            // module with 1 lesson
            dataHelper.getCourseModule(
                courseId = course.id,
                title = "Module 2",
                description = "Module 2 Description",
                listingOrder = 2,
            ).also { module2 ->
                dataHelper.getLesson(
                    courseModuleId = module2.id,
                    durationInSeconds = 100,
                )
            }
            // module with 1 deleted lesson
            dataHelper.getCourseModule(
                courseId = course.id,
                title = "Module 3",
                description = "Module 3 Description",
                listingOrder = 3,
            ).also { module3 ->
                dataHelper.getLesson(
                    courseModuleId = module3.id,
                    durationInSeconds = 1000,
                    entityModifier = { it.softDelete() },
                )
            }
            // deleted module with deleted lesson
            dataHelper.getCourseModule(
                id = 4.toUUID(),
                courseId = course.id,
                title = "Module 4",
                description = "Module 4 Description",
                listingOrder = 4,
                entityModifier = { it.softDelete() },
            ).also { module4 ->
                dataHelper.getLesson(
                    courseModuleId = module4.id,
                    durationInSeconds = 10000,
                    entityModifier = { it.softDelete() },
                )
            }
        }

        // when
        val result = underTest.handle(
            PublicGetPublicCourseQuery(
                courseId = 1.toUUID(),
                withRefresh = false,
            ),
        )

        // then
        result.run {
            courseId shouldBe 1.toUUID()
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            traderInfo shouldNotBe null
            traderInfo.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            }
            trailerUrl shouldBe "trailer-url"
            title shouldBe "Course"
            description shouldBe "Description"
            color shouldBe Color.ORANGE
            homepage shouldBe true
            modules shouldHaveSize 3
            moduleCount shouldBe 3
            totalDurationInSeconds shouldBe 120

            // Verify modules are ordered correctly
            modules[0].run {
                title shouldBe "Module 1"
                description shouldBe "Module 1 Description"
                listingOrder shouldBe 1
                lessonCount shouldBe 2
                totalDurationInSeconds shouldBe 20
            }

            modules[1].run {
                title shouldBe "Module 2"
                description shouldBe "Module 2 Description"
                listingOrder shouldBe 2
                lessonCount shouldBe 1
                totalDurationInSeconds shouldBe 100
            }

            modules[2].run {
                title shouldBe "Module 3"
                description shouldBe "Module 3 Description"
                listingOrder shouldBe 3
                lessonCount shouldBe 0
                totalDurationInSeconds shouldBe 0
            }
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
