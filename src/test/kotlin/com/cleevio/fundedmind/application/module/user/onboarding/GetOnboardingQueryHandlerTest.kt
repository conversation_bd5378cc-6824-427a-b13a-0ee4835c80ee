package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.DataTestHelper
import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.onboarding.query.GetOnboardingQuery
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.domain.user.toQuestionnaire
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetOnboardingQueryHandlerTest @Autowired constructor(
    private val underTest: GetOnboardingQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get onboarding without survey and questionnaire`() {
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.changeState(OnboardingState.MASTERCLASS_CHECKOUT)
            },
        )

        val result = underTest.handle(GetOnboardingQuery(1.toUUID()))

        result.run {
            userId shouldBe 1.toUUID()
            state shouldBe OnboardingState.MASTERCLASS_CHECKOUT
            studentTier shouldBe StudentTier.NO_TIER
            firstName shouldBe null
            lastName shouldBe null
            phone shouldBe null
            biography shouldBe null
            country shouldBe null
            profilePicture shouldBe null
            questionnaire shouldBe null
            location shouldBe null
            masterclassInOnboarding shouldBe false
        }
    }

    @Test
    fun `should get onboarding without profile picture`() {
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.changeState(OnboardingState.SURVEY)
                it.saveUserSurvey(
                    firstName = "Karel",
                    lastName = "Gott",
                    phone = "+************",
                    biography = "Ceske ESO",
                    country = Country.CZ,
                    firstNameVocative = "Karle",
                    lastNameVocative = "Gotte",
                    locationId = null,
                )
            },
        )

        val result = underTest.handle(GetOnboardingQuery(1.toUUID()))

        result.run {
            userId shouldBe 1.toUUID()
            state shouldBe OnboardingState.SURVEY
            studentTier shouldBe StudentTier.NO_TIER
            firstName shouldBe "Karel"
            lastName shouldBe "Gott"
            phone shouldBe "+************"
            biography shouldBe "Ceske ESO"
            country shouldBe Country.CZ
            profilePicture shouldBe null
            location shouldBe null
            masterclassInOnboarding shouldBe false
        }
    }

    @Test
    fun `should get onboarding with survey, questionnaire and profile picture`() {
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.saveUserSurvey(
                    firstName = "Karel",
                    lastName = "Gott",
                    phone = "+************",
                    biography = "Ceske ESO",
                    country = Country.CZ,
                    firstNameVocative = "Karle",
                    lastNameVocative = "Gotte",
                    locationId = null,
                )
                it.saveQuestionnaire(
                    questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput().toQuestionnaire(),
                )
                it.changeState(newState = OnboardingState.QUESTIONNAIRE)
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url1",
                        compressedFileUrl = "url1-comp",
                        blurHash = "1",
                    ).id,
                )
            },
        )

        val result = underTest.handle(
            GetOnboardingQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            userId shouldBe 1.toUUID()
            state shouldBe OnboardingState.QUESTIONNAIRE
            studentTier shouldBe StudentTier.NO_TIER
            firstName shouldBe "Karel"
            lastName shouldBe "Gott"
            phone shouldBe "+************"
            biography shouldBe "Ceske ESO"
            country shouldBe Country.CZ
            questionnaire shouldNotBe null
            questionnaire!!.keys shouldBe setOf("version", "data")
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "url1"
                imageCompressedUrl shouldBe "url1-comp"
                imageBlurHash shouldBe "1"
            }
            location shouldBe null
            masterclassInOnboarding shouldBe false
        }
    }

    @Test
    fun `should get onboarding with location`() {
        val locationId = dataHelper.getUserLocation(
            id = 2.toUUID(),
            street = "123 Main St",
            city = "Prague",
            postalCode = "12345",
            state = "Czech Republic",
            latitude = 50.0755,
            longitude = 14.4378,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.4,
        ).id

        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.saveUserSurvey(
                    firstName = "Karel",
                    lastName = "Gott",
                    phone = "+************",
                    biography = "Ceske ESO",
                    country = Country.CZ,
                    firstNameVocative = "Karle",
                    lastNameVocative = "Gotte",
                    locationId = locationId,
                )
                it.changeState(newState = OnboardingState.SURVEY)
            },
        )

        val result = underTest.handle(
            GetOnboardingQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            userId shouldBe 1.toUUID()
            state shouldBe OnboardingState.SURVEY
            studentTier shouldBe StudentTier.NO_TIER
            firstName shouldBe "Karel"
            lastName shouldBe "Gott"
            phone shouldBe "+************"
            biography shouldBe "Ceske ESO"
            country shouldBe Country.CZ
            profilePicture shouldBe null
            location shouldNotBe null
            location!!.run {
                street shouldBe "123 Main St"
                city shouldBe "Prague"
                postalCode shouldBe "12345"
                state shouldBe "Czech Republic"
                location.latitude shouldBe 50.0755
                location.longitude shouldBe 14.4378
                obfuscatedLocation.latitude shouldBe 50.0
                obfuscatedLocation.longitude shouldBe 14.4
            }
            masterclassInOnboarding shouldBe false
        }
    }

    @Test
    fun `should get onboarding with location with null address fields`() {
        val locationId = dataHelper.getUserLocation(
            id = 2.toUUID(),
            street = null,
            city = null,
            postalCode = null,
            state = null,
        ).id

        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.upgradeToMasterclass()
                it.saveUserSurvey(
                    firstName = "Karel",
                    lastName = "Gott",
                    phone = "+************",
                    biography = "Ceske ESO",
                    country = Country.CZ,
                    firstNameVocative = "Karle",
                    lastNameVocative = "Gotte",
                    locationId = locationId,
                )
                it.changeState(newState = OnboardingState.SURVEY)
            },
        )

        val result = underTest.handle(
            GetOnboardingQuery(
                studentId = 1.toUUID(),
            ),
        )

        result.run {
            userId shouldBe 1.toUUID()
            location shouldNotBe null
            location!!.run {
                street shouldBe null
                city shouldBe null
                postalCode shouldBe null
                state shouldBe null
            }
            studentTier shouldBe StudentTier.MASTERCLASS
            masterclassInOnboarding shouldBe true
        }
    }
}
