package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.query.ListMeetingsInCalendarQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate

class ListMeetingsInCalendarQueryHandlerTest @Autowired constructor(
    private val underTest: ListMeetingsInCalendarQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list meetings in calendar - verify mappings`() {
        // Given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        dataHelper.getMeeting(
            id = 1.toUUID(),
            name = "Meeting 1",
            color = Color.RED,
            startAt = "2025-04-10T10:00:00Z".toInstant(),
            finishAt = "2025-04-10T12:00:00Z".toInstant(),
            description = "Meeting description",
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
            meetingUrl = "meeting-url",
            recordingUrl = "recording-url",
            traderIds = listOf(1.toUUID(), 2.toUUID()),
        )

        // When
        val result = underTest.handle(
            ListMeetingsInCalendarQuery(
                userId = 1.toUUID(),
                filter = ListMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        // Then
        result.data shouldHaveSize 1
        result.data.single().run {
            meetingId shouldBe 1.toUUID()
            name shouldBe "Meeting 1"
            color shouldBe Color.RED
            startAt shouldBe "2025-04-10T10:00:00Z".toInstant()
            finishAt shouldBe "2025-04-10T12:00:00Z".toInstant()
            isLockedForMe shouldBe false
        }
    }

    @Test
    fun `should list meetings in calendar without deleted meetings`() {
        // Given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getMeeting(id = 1.toUUID())
        dataHelper.getMeeting(id = 2.toUUID(), entityModifier = { it.softDelete() })
        dataHelper.getMeeting(id = 3.toUUID())

        // When
        val result = underTest.handle(
            ListMeetingsInCalendarQuery(
                userId = 1.toUUID(),
                filter = ListMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 1, 1),
                    endDate = LocalDate.of(2025, 12, 31),
                ),
            ),
        )

        // Then
        result.data.map { it.meetingId } shouldBe listOf(1.toUUID(), 3.toUUID())
    }

    @Test
    fun `should lock meeting for student without Discord when only Discord users invited`() {
        val student = dataHelper.getAppUser(id = 100.toUUID(), userRole = UserRole.STUDENT)
            .also { dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP) }
        val meeting = dataHelper.getMeeting(
            id = 200.toUUID(),
            invitedTiers = emptyList(),
            invitedDiscordUsers = true,
            startAt = "2025-04-10T10:00:00Z".toInstant(),
            finishAt = "2025-04-10T12:00:00Z".toInstant(),
        )

        val result = underTest.handle(
            ListMeetingsInCalendarQuery(
                userId = student.id,
                filter = ListMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        result.data.single { it.meetingId == meeting.id }.isLockedForMe shouldBe true
    }

    @Test
    fun `should unlock meeting for student with Discord when only Discord users invited`() {
        val student = dataHelper.getAppUser(id = 101.toUUID(), userRole = UserRole.STUDENT)
            .also {
                dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP, entityModifier = { s ->
                    s.activateDiscordSubscription(expiresAt = "2099-01-01T00:00:00Z".toInstant())
                })
            }
        val meeting = dataHelper.getMeeting(
            id = 201.toUUID(),
            invitedTiers = emptyList(),
            invitedDiscordUsers = true,
            startAt = "2025-04-11T10:00:00Z".toInstant(),
            finishAt = "2025-04-11T12:00:00Z".toInstant(),
        )

        val result = underTest.handle(
            ListMeetingsInCalendarQuery(
                userId = student.id,
                filter = ListMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        result.data.single { it.meetingId == meeting.id }.isLockedForMe shouldBe false
    }

    @Test
    fun `should lock meeting for Basecamp student when only Masterclass invited`() {
        val student = dataHelper.getAppUser(id = 102.toUUID(), userRole = UserRole.STUDENT)
            .also { dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP) }
        val meeting = dataHelper.getMeeting(
            id = 202.toUUID(),
            invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
            startAt = "2025-04-12T10:00:00Z".toInstant(),
            finishAt = "2025-04-12T12:00:00Z".toInstant(),
        )

        val result = underTest.handle(
            ListMeetingsInCalendarQuery(
                userId = student.id,
                filter = ListMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        result.data.single { it.meetingId == meeting.id }.isLockedForMe shouldBe true
    }

    @Test
    fun `should unlock meeting for Masterclass student when only Masterclass invited`() {
        val student = dataHelper.getAppUser(id = 103.toUUID(), userRole = UserRole.STUDENT)
            .also { dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS) }
        val meeting = dataHelper.getMeeting(
            id = 203.toUUID(),
            invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
            startAt = "2025-04-13T10:00:00Z".toInstant(),
            finishAt = "2025-04-13T12:00:00Z".toInstant(),
        )

        val result = underTest.handle(
            ListMeetingsInCalendarQuery(
                userId = student.id,
                filter = ListMeetingsInCalendarQuery.Filter(
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30),
                ),
            ),
        )

        result.data.single { it.meetingId == meeting.id }.isLockedForMe shouldBe false
    }
}
