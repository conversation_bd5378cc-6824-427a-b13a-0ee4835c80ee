package com.cleevio.fundedmind.application.module.comment.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.comment.command.UserLikesCommentCommand
import com.cleevio.fundedmind.domain.comment.CommentLikeRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class UserLikesCommentCommandHandlerTest @Autowired constructor(
    private val underTest: UserLikesCommentCommandHandler,
    private val commentLikeRepository: CommentLikeRepository,
) : IntegrationTest() {

    @Test
    fun `should like existing comment for existing lesson`() {
        // given
        val trader = dataHelper.getTrader(id = 0.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val user = dataHelper.getAppUser(id = 999.toUUID())
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = user.id,
            lessonId = lesson.id,
        )

        // when
        val result = underTest.handle(
            UserLikesCommentCommand(
                appUserId = 999.toUUID(),
                commentId = 1000.toUUID(),
            ),
        )

        // then
        commentLikeRepository.getReferenceById(result.id).run {
            appUserId shouldBe 999.toUUID()
            commentId shouldBe 1000.toUUID()
        }
    }
}
