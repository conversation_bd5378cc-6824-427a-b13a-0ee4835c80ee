package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.product.query.AutocompleteProductQuery
import com.cleevio.fundedmind.parseIntegerList
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class AutocompleteProductQueryHandlerTest @Autowired constructor(
    private val underTest: AutocompleteProductQueryHandler,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "null; [1,2,3,4,5]",
            "a;    [1,6]",
            "aa;   []",
            "9;    []",
            "c;    [3,8]",
            "č;    [3,8]",
        ],
    )
    fun `should return filtered and sorted products based on searchString`(
        searchString: String?,
        expectedIdsRaw: String,
    ) {
        // given
        val expectedIds: List<UUID> = expectedIdsRaw.parseIntegerList().map { it.toUUID() }

        dataHelper.getTrader(id = 1.toUUID())

        dataHelper.getProduct(id = 1.toUUID(), traderId = 1.toUUID(), name = "a")
        dataHelper.getProduct(id = 2.toUUID(), traderId = 1.toUUID(), name = "b")
        dataHelper.getProduct(id = 3.toUUID(), traderId = 1.toUUID(), name = "č")
        dataHelper.getProduct(id = 4.toUUID(), traderId = 1.toUUID(), name = "d")
        dataHelper.getProduct(id = 5.toUUID(), traderId = 1.toUUID(), name = "e")
        dataHelper.getProduct(id = 6.toUUID(), traderId = 1.toUUID(), name = "xa")
        dataHelper.getProduct(id = 7.toUUID(), traderId = 1.toUUID(), name = "yb")
        dataHelper.getProduct(id = 8.toUUID(), traderId = 1.toUUID(), name = "zc")

        // when
        val result: AutocompleteProductQuery.Result = underTest.handle(
            defaultQuery(traderId = 1.toUUID(), searchString = searchString, limit = 5),
        )

        // then
        result.data.map { it.productId } shouldBe expectedIds
    }

    @Test
    fun `should return empty result if no products found`() {
        val result = underTest.handle(
            defaultQuery(traderId = 1.toUUID(), searchString = null),
        )

        result.data shouldBe emptyList()
    }

    @Test
    fun `should return only products related to trader`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        dataHelper.getProduct(id = 1.toUUID(), traderId = 1.toUUID(), name = "product of trader 1")
        dataHelper.getProduct(id = 2.toUUID(), traderId = 2.toUUID(), name = "product of trader 2")

        val result = underTest.handle(
            defaultQuery(traderId = 2.toUUID(), searchString = null),
        )

        result.data.single().run {
            productId shouldBe 2.toUUID()
            name shouldBe "product of trader 2"
        }
    }

    private fun defaultQuery(
        traderId: UUID,
        limit: Int = 5,
        searchString: String? = null,
    ) = AutocompleteProductQuery(
        limit = limit,
        filter = AutocompleteProductQuery.Filter(
            traderId = traderId,
            searchString = searchString,
        ),
    )
}
