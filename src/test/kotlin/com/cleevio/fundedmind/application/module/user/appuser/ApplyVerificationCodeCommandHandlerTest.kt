package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.appuser.command.ApplyVerificationCodeCommand
import com.cleevio.fundedmind.domain.user.appuser.VerificationCodeRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.VerificationCodeStatus
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.Instant
import java.time.temporal.ChronoUnit

class ApplyVerificationCodeCommandHandlerTest @Autowired constructor(
    private val underTest: ApplyVerificationCodeCommandHandler,
    private val verificationCodeRepository: VerificationCodeRepository,
) : IntegrationTest() {

    @Test
    fun `should use verification code and return VALID status`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID())

        dataHelper.getVerificationCode(
            id = 0.toUUID(),
            appUserId = 1.toUUID(),
            code = "1234",
        )

        // when
        val result = underTest.handle(
            ApplyVerificationCodeCommand(userId = 1.toUUID(), code = "1234"),
        )

        // then
        result.codeStatus shouldBe VerificationCodeStatus.VALID

        verificationCodeRepository.findByIdOrNull(0.toUUID())!!.run {
            status shouldBe VerificationCodeStatus.USED
        }
    }

    @Test
    fun `should return EXPIRED status if verification code is expired`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID())

        dataHelper.getVerificationCode(
            id = 0.toUUID(),
            appUserId = 1.toUUID(),
            code = "1234",
            expiresAt = Instant.now().minus(1, ChronoUnit.MINUTES),
        )

        // when
        val result = underTest.handle(
            ApplyVerificationCodeCommand(userId = 1.toUUID(), code = "1234"),
        )

        // then
        result.codeStatus shouldBe VerificationCodeStatus.EXPIRED
        verificationCodeRepository.findByIdOrNull(0.toUUID())!!.run {
            status shouldBe VerificationCodeStatus.EXPIRED
        }
    }

    @Test
    fun `should return USED status if verification code is used`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID())

        dataHelper.getVerificationCode(
            id = 0.toUUID(),
            appUserId = 1.toUUID(),
            code = "1234",
            expiresAt = Instant.now().plus(10, ChronoUnit.MINUTES),
            entityModifier = {
                it.markAsUsed()
            },
        )

        // when
        val result = underTest.handle(
            ApplyVerificationCodeCommand(userId = 1.toUUID(), code = "1234"),
        )

        // then
        result.codeStatus shouldBe VerificationCodeStatus.USED

        verificationCodeRepository.findByIdOrNull(0.toUUID())!!.run {
            status shouldBe VerificationCodeStatus.USED
        }
    }

    @Test
    fun `should return USED status if verification is both expired and used`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID())

        dataHelper.getVerificationCode(
            id = 0.toUUID(),
            appUserId = 1.toUUID(),
            code = "1234",
            expiresAt = Instant.now().minus(10, ChronoUnit.MINUTES),
            entityModifier = {
                it.markAsUsed()
            },
        )

        // when
        val result = underTest.handle(
            ApplyVerificationCodeCommand(userId = 1.toUUID(), code = "1234"),
        )

        // then
        result.codeStatus shouldBe VerificationCodeStatus.USED

        verificationCodeRepository.findByIdOrNull(0.toUUID())!!.run {
            status shouldBe VerificationCodeStatus.USED
        }
    }
}
