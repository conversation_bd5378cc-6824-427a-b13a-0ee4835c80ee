package com.cleevio.fundedmind.application.module.gamedocument.service

import com.cleevio.fundedmind.infrastructure.properties.GameDocumentUploadRateLimitProperties
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class GameDocumentUploadRateLimitServiceUnitTest {

    private val underTest = GameDocumentUploadRateLimitService(
        properties = GameDocumentUploadRateLimitProperties(),
    )

    @Test
    fun `should allow upload when no attempts have been made`() {
        // when
        val userId = 1.toUUID()

        val canUpload = underTest.isAllowedToUpload(userId)
        val remainingAttempts = underTest.getRemainingAttempts(userId)

        // then
        canUpload shouldBe true
        remainingAttempts shouldBe 5
    }

    @Test
    fun `should allow upload when fewer than max attempts have been made`() {
        // given
        val userId = 1.toUUID()

        underTest.recordAttempt(userId)
        underTest.recordAttempt(userId)
        underTest.recordAttempt(userId)

        // when
        val canUpload = underTest.isAllowedToUpload(userId)
        val remainingAttempts = underTest.getRemainingAttempts(userId)

        // then
        canUpload shouldBe true
        remainingAttempts shouldBe 2
    }

    @Test
    fun `should not allow upload when max attempts have been made`() {
        // given
        val userId = 1.toUUID()

        repeat(5) {
            underTest.recordAttempt(userId)
        }

        // when
        val canUpload = underTest.isAllowedToUpload(userId)
        val remainingAttempts = underTest.getRemainingAttempts(userId)

        // then
        canUpload shouldBe false
        remainingAttempts shouldBe 0
    }

    @Test
    fun `should track attempts separately for different users`() {
        // given
        val userId1 = 1.toUUID()
        val userId2 = 2.toUUID()

        // when
        underTest.recordAttempt(userId1)
        underTest.recordAttempt(userId1)
        underTest.recordAttempt(userId2)

        // then
        underTest.getRemainingAttempts(userId1) shouldBe 3
        underTest.getRemainingAttempts(userId2) shouldBe 4
    }
}
