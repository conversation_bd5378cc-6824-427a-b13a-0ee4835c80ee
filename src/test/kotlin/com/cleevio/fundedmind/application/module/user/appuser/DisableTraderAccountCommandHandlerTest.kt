package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.appuser.command.DisableTraderAccountCommand
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserHasWrongRoleException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasMentoringMeetingToAttendException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasSaleableProductException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasUnfinishedMentoringException
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DisableTraderAccountCommandHandlerTest @Autowired constructor(
    private val underTest: DisableTraderAccountCommandHandler,
    private val appUserRepository: AppUserRepository,
) : IntegrationTest() {

    @Test
    fun `should disable trader account - trader has no products`() {
        every { firebaseService.disableUser(email = "<EMAIL>") } returns Result.success(Unit)

        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.TRADER,
        )

        dataHelper.getTrader(1.toUUID())

        underTest.handle(
            DisableTraderAccountCommand(traderId = 1.toUUID()),
        )

        appUserRepository.findByIdOrNull(1.toUUID())!!.accountActive shouldBe false

        verify { firebaseService.disableUser(email = "<EMAIL>") }
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = [UserRole.TRADER_ROLE],
        mode = EnumSource.Mode.EXCLUDE,
    )
    fun `should throw if account does not belong to TRADER role`(userRole: UserRole) {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = userRole)

        shouldThrow<UserHasWrongRoleException> {
            underTest.handle(
                DisableTraderAccountCommand(traderId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if trader has at least one saleable product`() {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)

        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id, entityModifier = { it.makeSaleable() })
            dataHelper.getProduct(id = 2.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        }

        shouldThrow<TraderHasSaleableProductException> {
            underTest.handle(
                DisableTraderAccountCommand(traderId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should pass if trader has one saleable product but it is deleted`() {
        every { firebaseService.disableUser(email = "<EMAIL>") } returns Result.success(Unit)

        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.TRADER,
        )

        dataHelper.getTrader(1.toUUID()).also { trader ->
            dataHelper.getProduct(
                id = 1.toUUID(),
                traderId = trader.id,
                entityModifier = {
                    it.makeSaleable()
                    it.softDelete()
                },
            )
            dataHelper.getProduct(
                id = 2.toUUID(),
                traderId = trader.id,
                entityModifier = { it.makeUnsaleable() },
            )
        }

        underTest.handle(
            DisableTraderAccountCommand(traderId = 1.toUUID()),
        )

        appUserRepository.findByIdOrNull(1.toUUID())!!.accountActive shouldBe false

        verify { firebaseService.disableUser(email = "<EMAIL>") }
    }

    @Test
    fun `should throw if trader has unfinished mentorings`() {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getProduct(id = 3.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        }

        // Create student
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring with sessions left
        dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = 2.toUUID(),
            productId = 3.toUUID(),
            sessionCount = 3,
            useSessions = 1,
        )

        shouldThrow<TraderHasUnfinishedMentoringException> {
            underTest.handle(
                DisableTraderAccountCommand(traderId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should pass if trader has mentorings but all are finished`() {
        every { firebaseService.disableUser(email = "<EMAIL>") } returns Result.success(Unit)

        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.TRADER,
        )
        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getProduct(id = 3.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        }

        // Create student
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring with no sessions left
        dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = 2.toUUID(),
            productId = 3.toUUID(),
            sessionCount = 3,
            useSessions = 3,
        )

        underTest.handle(
            DisableTraderAccountCommand(traderId = 1.toUUID()),
        )

        appUserRepository.findByIdOrNull(1.toUUID())!!.accountActive shouldBe false
        verify { firebaseService.disableUser(email = "<EMAIL>") }
    }

    @Test
    fun `should throw if trader has mentoring meetings to attend`() {
        val now = "2025-04-14T10:00:00Z".toInstant()

        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getProduct(id = 3.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        }

        // Create student
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring for the meeting
        val mentoring = dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = 2.toUUID(),
            productId = 3.toUUID(),
            sessionCount = 1,
            useSessions = 1,
        )

        // Create a mentoring meeting - it has started but not yet finished
        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.plusSeconds(3600), // 1 hour in the past
            finishAt = now.plusSeconds(7200), // 1 hour in the future
        )

        shouldThrow<TraderHasMentoringMeetingToAttendException> {
            underTest.handle(
                DisableTraderAccountCommand(traderId = 1.toUUID(), now = now),
            )
        }
    }

    @Test
    fun `should pass if trader has mentoring meetings but all are in the past`() {
        every { firebaseService.disableUser(email = "<EMAIL>") } returns Result.success(Unit)

        val now = "2025-04-14T10:00:00Z".toInstant()

        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.TRADER,
        )
        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getProduct(id = 3.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        }

        // Create student
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring for the meeting
        val mentoring = dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = 2.toUUID(),
            productId = 3.toUUID(),
            sessionCount = 1,
            useSessions = 1,
        )

        // Create a mentoring meeting in the past
        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.minusSeconds(7200), // 2 hours in the past
            finishAt = now.minusSeconds(3600), // 1 hour in the past
        )

        underTest.handle(
            DisableTraderAccountCommand(traderId = 1.toUUID(), now = now),
        )

        appUserRepository.findByIdOrNull(1.toUUID())!!.accountActive shouldBe false
        verify { firebaseService.disableUser(email = "<EMAIL>") }
    }

    @Test
    fun `should pass if trader has future mentoring meetings but all are modified`() {
        every { firebaseService.disableUser(email = "<EMAIL>") } returns Result.success(Unit)

        val now = "2025-04-14T10:00:00Z".toInstant()

        dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.TRADER,
        )
        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getProduct(id = 3.toUUID(), traderId = trader.id, entityModifier = { it.makeUnsaleable() })
        }

        // Create student
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 2.toUUID())

        // Create a mentoring for the meeting
        val mentoring = dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = 2.toUUID(),
            productId = 3.toUUID(),
            sessionCount = 1,
            useSessions = 1,
        )

        // Create a future mentoring meeting that is cancelled
        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.plusSeconds(3600), // 1 hour in the future
            finishAt = now.plusSeconds(7200), // 2 hours in the future
            entityModifier = {
                it.cancel(
                    reason = "Test cancellation",
                    initiatorType = InitiatorType.MENTOR,
                )
            },
        )

        // Create a future mentoring meeting that is cancelled
        dataHelper.getMentoringMeeting(
            id = 2.toUUID(),
            mentoringId = mentoring.id,
            startAt = now.plusSeconds(3600), // 1 hour in the future
            finishAt = now.plusSeconds(7200), // 2 hours in the future
            entityModifier = {
                it.reschedule(
                    reason = "Test cancellation",
                    initiatorType = InitiatorType.MENTOR,
                )
            },
        )

        underTest.handle(
            DisableTraderAccountCommand(traderId = 1.toUUID(), now = now),
        )

        appUserRepository.findByIdOrNull(1.toUUID())!!.accountActive shouldBe false
        verify { firebaseService.disableUser(email = "<EMAIL>") }
    }
}
