package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.appuser.command.SendVerificationCodeCommand
import com.cleevio.fundedmind.application.module.user.appuser.exception.VerificationCodeAlreadyExistsException
import com.cleevio.fundedmind.domain.user.appuser.VerificationCodeRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.VerificationCodeStatus
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class SendVerificationCodeCommandHandlerTest @Autowired constructor(
    private val underTest: SendVerification<PERSON><PERSON><PERSON><PERSON>mandHand<PERSON>,
    private val verificationCodeRepository: VerificationCodeRepository,
) : IntegrationTest() {

    @Test
    fun `should create and send verification code`() {
        mockkStatic(Instant::class)
        every { Instant.now() } returns "2025-01-01T00:00:00Z".toInstant()

        every { sendEmailService.sendEmailOtpEmail(any()) } just Runs

        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>")

        underTest.handle(
            SendVerificationCodeCommand(
                userId = 1.toUUID(),
                code = "1234",
            ),
        )

        val verificationCode = verificationCodeRepository.findAll().single().apply {
            appUserId shouldBe 1.toUUID()
            code shouldBe "1234"
            expiresAt shouldBe "2025-01-01T00:30:00Z".toInstant() // now + 30 minutes
        }

        verify {
            Instant.now()
            sendEmailService.sendEmailOtpEmail(verificationCode.id)
        }
    }

    @Test
    fun `should expire previous valid verification code`() {
        every { sendEmailService.sendEmailOtpEmail(any()) } just Runs

        // given
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>")
        dataHelper.getVerificationCode(
            id = 1.toUUID(),
            appUserId = 1.toUUID(),
            code = "1234",
            expiresAt = Instant.now().plus(30, ChronoUnit.MINUTES),
        )

        underTest.handle(
            SendVerificationCodeCommand(
                userId = 1.toUUID(),
                code = "7890",
            ),
        )

        val verificationCode = verificationCodeRepository.findAll().run {
            this.size shouldBe 2
            this.single { it.id == 1.toUUID() }.run {
                code shouldBe "1234"
                status shouldBe VerificationCodeStatus.EXPIRED
            }
            this.single { it.code == "7890" }.status shouldBe VerificationCodeStatus.VALID
            return@run this.single { it.code == "7890" }
        }

        verify {
            sendEmailService.sendEmailOtpEmail(verificationCode.id)
        }
    }

    @Test
    fun `should throw if verification code already exists for user`() {
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>")
        dataHelper.getVerificationCode(
            appUserId = 1.toUUID(),
            code = "1234",
        ).also {
            it.status shouldBe VerificationCodeStatus.VALID
        }

        shouldThrow<VerificationCodeAlreadyExistsException> {
            underTest.handle(
                SendVerificationCodeCommand(
                    userId = 1.toUUID(),
                    code = "1234",
                ),
            )
        }
    }

    @Test
    fun `should throw if verification code already exists for user and it is used`() {
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>")
        dataHelper.getVerificationCode(
            appUserId = 1.toUUID(),
            code = "1234",
            entityModifier = { it.markAsUsed() },
        ).also {
            it.status shouldBe VerificationCodeStatus.USED
        }

        shouldThrow<VerificationCodeAlreadyExistsException> {
            underTest.handle(
                SendVerificationCodeCommand(
                    userId = 1.toUUID(),
                    code = "1234",
                ),
            )
        }
    }

    @Test
    fun `should throw if verification code already exists for user and it is expired`() {
        dataHelper.getAppUser(id = 1.toUUID(), email = "<EMAIL>")
        dataHelper.getVerificationCode(
            appUserId = 1.toUUID(),
            code = "1234",
            expiresAt = Instant.now().minus(1, ChronoUnit.MINUTES),
        ).also {
            it.status shouldBe VerificationCodeStatus.EXPIRED
        }

        shouldThrow<VerificationCodeAlreadyExistsException> {
            underTest.handle(
                SendVerificationCodeCommand(
                    userId = 1.toUUID(),
                    code = "1234",
                ),
            )
        }
    }
}
