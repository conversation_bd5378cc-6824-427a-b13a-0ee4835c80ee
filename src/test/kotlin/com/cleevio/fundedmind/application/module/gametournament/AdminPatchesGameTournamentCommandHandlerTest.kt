package com.cleevio.fundedmind.application.module.gametournament

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gametournament.command.AdminPatchesGameTournamentCommand
import com.cleevio.fundedmind.application.module.gametournament.exception.GameTournamentNotFoundException
import com.cleevio.fundedmind.domain.gametournament.GameTournamentRepository
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate

class AdminPatchesGameTournamentCommandHandlerTest @Autowired constructor(
    private val underTest: AdminPatchesGameTournamentCommandHandler,
    private val gameTournamentRepository: GameTournamentRepository,
) : IntegrationTest() {

    @Test
    fun `should update existing game tournament`() {
        val existingOffset = dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 6, 30),
        )

        underTest.handle(
            AdminPatchesGameTournamentCommand(
                gameTournamentId = null,
                today = LocalDate.of(2025, 1, 1),
                offset = BigDecimal("10.50"),
            ),
        )

        gameTournamentRepository.findByIdOrNull(existingOffset.id)!!.run {
            payoutOffset shouldBeEqualComparingTo BigDecimal("10.50")
        }
    }

    @Test
    fun `should update existing game tournament with negative value`() {
        val existingOffset = dataHelper.getGameTournament(
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 6, 30),
        )

        underTest.handle(
            AdminPatchesGameTournamentCommand(
                gameTournamentId = null,
                today = LocalDate.of(2025, 1, 1),
                offset = BigDecimal("-10.50"),
            ),
        )

        gameTournamentRepository.findByIdOrNull(existingOffset.id)!!.run {
            payoutOffset shouldBeEqualComparingTo BigDecimal("-10.50")
        }
    }

    @Test
    fun `should throw exception when game tournament not found`() {
        shouldThrow<GameTournamentNotFoundException> {
            underTest.handle(
                AdminPatchesGameTournamentCommand(
                    gameTournamentId = null,
                    today = LocalDate.of(2025, 1, 1),
                    offset = BigDecimal("5.00"),
                ),
            )
        }
    }
}
