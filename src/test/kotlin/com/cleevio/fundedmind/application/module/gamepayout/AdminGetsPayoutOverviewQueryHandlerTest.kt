package com.cleevio.fundedmind.application.module.gamepayout

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamepayout.query.AdminGetsPayoutOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate

class AdminGetsPayoutOverviewQueryHandlerTest @Autowired constructor(
    private val underTest: AdminGetsPayoutOverviewQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return payout overview with approved payouts and offset`() {
        // Create students
        val student1 = dataHelper.getAppUser(id = 201.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }
        val student2 = dataHelper.getAppUser(id = 202.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.EXCLUSIVE)
        }

        // Create approved payout documents for the year
        val approvedPayout1 = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 6, 15),
            entityModifier = { it.approveAwaiting() },
        )

        val approvedPayout2 = dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("500.50"),
            payoutDate = LocalDate.of(2025, 8, 20),
            entityModifier = { it.approveAwaiting() },
        )

        // Create a waiting payout (should not be counted)
        val waitingPayout = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("300.00"),
            payoutDate = LocalDate.of(2025, 9, 10),
        ) // state remains WAITING

        // Create a certificate (should not be counted)
        dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            payoutDate = LocalDate.of(2025, 7, 5),
            entityModifier = { it.approveAwaiting() },
        )

        // Create payout offset for the year
        dataHelper.getGameTournament(
            payoutOffset = BigDecimal("250.75"),
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 6, 30),
        )

        val result = underTest.handle(
            AdminGetsPayoutOverviewQuery(today = LocalDate.of(2025, 1, 2)),
        )

        result.run {
            approvedPayouts shouldBe 2
            realTotalPayout shouldBeEqualComparingTo BigDecimal("1500.50") // 1000.00 + 500.50
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("1751.25") // 1500.50 + 250.75
            waitingDocumentsCount shouldBe 1 // waitingPayout
            year shouldBe 2025
        }
    }

    @Test
    fun `should return zero values when no approved payouts exist`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        // Create only waiting/denied payouts
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("100.00"),
            payoutDate = LocalDate.of(2024, 5, 10),
        ) // state remains WAITING

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("200.00"),
            payoutDate = LocalDate.of(2024, 6, 15),
            entityModifier = { it.denyAwaiting("Rejected") },
        )

        // Create payout offset for the year
        dataHelper.getGameTournament(
            payoutOffset = BigDecimal("100.00"),
            startAt = LocalDate.of(2024, 1, 1),
            finishAt = LocalDate.of(2024, 6, 30),
        )

        val result = underTest.handle(
            AdminGetsPayoutOverviewQuery(today = LocalDate.of(2024, 1, 2)),
        )

        result.run {
            approvedPayouts shouldBe 0
            realTotalPayout shouldBeEqualComparingTo BigDecimal.ZERO
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("100.00") // 0 + 100.00
            waitingDocumentsCount shouldBe 1 // one waiting document
            year shouldBe 2024
        }
    }

    @Test
    fun `should return zero offset when no offset exists for year`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 204.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create approved payout
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("750.25"),
            payoutDate = LocalDate.of(2023, 12, 1),
            entityModifier = { it.approveAwaiting() },
        )

        // No offset created for this year

        val result = underTest.handle(
            AdminGetsPayoutOverviewQuery(today = LocalDate.of(2023, 1, 2)),
        )

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("750.25")
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("750.25") // 750.25 + 0
            waitingDocumentsCount shouldBe 0 // no waiting documents
            year shouldBe 2023
        }
    }

    @Test
    fun `should filter payouts by year correctly`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 205.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.EXCLUSIVE)
        }

        // Create approved payout for target year
        dataHelper.getGameDocument(
            createdTimestamp = "2025-03-15T10:00:00Z".toInstant(),
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("400.00"),
            payoutDate = LocalDate.of(2025, 3, 15), // target year
            entityModifier = { it.approveAwaiting() },
        )

        // Create approved payout with payout date in different year (should not be counted)
        dataHelper.getGameDocument(
            createdTimestamp = "2025-01-01T10:00:00Z".toInstant(),
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("200.00"),
            payoutDate = LocalDate.of(2024, 11, 20), // different year - should not be counted
            entityModifier = { it.approveAwaiting() },
        )

        // Create approved payout with payout date in different year (should not be counted)
        dataHelper.getGameDocument(
            createdTimestamp = "2024-11-20T10:00:00Z".toInstant(),
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("800.00"),
            payoutDate = LocalDate.of(2024, 11, 20), // different year - should not be counted
            entityModifier = { it.approveAwaiting() },
        )

        // Create offset for target year
        dataHelper.getGameTournament(
            payoutOffset = BigDecimal("50.00"),
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 6, 30),
        )

        val result = underTest.handle(
            AdminGetsPayoutOverviewQuery(today = LocalDate.of(2025, 1, 2)),
        )

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("400.00") // Only the payout with payout date in 2025
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("450.00") // 400.00 + 50.00
            waitingDocumentsCount shouldBe 0 // no waiting documents
            year shouldBe 2025
        }
    }

    @Test
    fun `should handle negative offset correctly`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 207.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        // Create approved payout
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 4, 10),
            entityModifier = { it.approveAwaiting() },
        )

        // Create negative offset
        dataHelper.getGameTournament(
            payoutOffset = BigDecimal("-150.50"),
            startAt = LocalDate.of(2025, 1, 1),
            finishAt = LocalDate.of(2025, 6, 30),
        )

        val result = underTest.handle(
            AdminGetsPayoutOverviewQuery(today = LocalDate.of(2025, 1, 2)),
        )

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("1000.00")
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("849.50") // 1000.00 + (-150.50)
            waitingDocumentsCount shouldBe 0 // no waiting documents
            year shouldBe 2025
        }
    }
}
