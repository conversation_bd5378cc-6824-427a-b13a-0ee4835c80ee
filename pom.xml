<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.4</version>
        <relativePath/>
    </parent>

    <groupId>com.cleevio</groupId>
    <artifactId>funded-mind-api</artifactId>
    <version>0.0.1</version>
    <name>FundedMind API</name>
    <description>FundedMind API</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>

        <kotlin.version>2.1.21</kotlin.version>
        <kotlin.compiler.jvmTarget>${java.version}</kotlin.compiler.jvmTarget>
        <kotlin.code.style>official</kotlin.code.style>

        <postgres.version>15.12</postgres.version>

        <classgraph.version>4.8.181</classgraph.version>
        <datasource-decorator.version>1.12.0</datasource-decorator.version>
        <firebase.version>9.5.0</firebase.version>
        <git-commit-maven-plugin.version>9.0.2</git-commit-maven-plugin.version>
        <groovy-maven-plugin.version>2.1.1</groovy-maven-plugin.version>
        <handlebars.version>4.4.0</handlebars.version>
        <hypersistence.version>3.10.3</hypersistence.version>
        <jacoco-maven-plugin.version>0.8.13</jacoco-maven-plugin.version>
        <jts.version>1.20.0</jts.version>
        <kotest.version>5.9.1</kotest.version>
        <ktlint-maven-plugin.version>3.5.0</ktlint-maven-plugin.version>
        <loki-logback.version>1.6.0</loki-logback.version>
        <maven-dependency-plugin.version>3.8.1</maven-dependency-plugin.version>
        <maven-enforcer-plugin.version>3.6.1</maven-enforcer-plugin.version>
        <maven-surefire-plugin.version>3.5.3</maven-surefire-plugin.version>
        <maven-versions-plugin.version>2.18.0</maven-versions-plugin.version>
        <mockk-jvm.version>1.14.5</mockk-jvm.version>
        <oauth2.version>6.4.2</oauth2.version>
        <opentelemetry.instrumentation.version>2.7.0</opentelemetry.instrumentation.version>
        <sentry.version>8.18.0</sentry.version>
        <shedlock.version>6.9.2</shedlock.version>
        <springdoc.version>2.8.9</springdoc.version>
        <springdoc-kotlin.version>1.8.0</springdoc-kotlin.version>
        <springmockk.version>4.0.2</springmockk.version>
        <stripe.version>29.1.0</stripe.version> <!-- if updating version, upgrade API version in dashboard to match!-->
        <uuid-generator.version>5.1.0</uuid-generator.version>

        <!-- Java 17 enforces strong encapsulation, ktlint needs to access some classes though -->
        <argLine>
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.time=ALL-UNNAMED
            --add-opens java.base/java.io=ALL-UNNAMED
        </argLine>
    </properties>

    <repositories>
        <repository>
            <id>mavenCentral</id>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
        <repository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </repository>
    </distributionManagement>

    <dependencies>

        <!-- Cleevio -->
        <dependency>
            <groupId>com.cleevio.library</groupId>
            <artifactId>spring-boot-starter-locking-handler</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.cleevio.library</groupId>
            <artifactId>spring-boot-starter-exception-handler</artifactId>
            <version>1.0.4</version>
        </dependency>

        <!-- Kotlin -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>

        <!-- Development UX -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-devtools</artifactId>-->
        <!--            <scope>runtime</scope>-->
        <!--            <optional>true</optional>-->
        <!--        </dependency>-->

        <!-- Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-blackbird</artifactId>
        </dependency>

        <!-- Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot DATA JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Springdoc & Swagger -->
        <!-- Imports Swagger UI -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc.version}</version>
        </dependency>
        <!-- Imports Swagger annotations -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
            <version>${springdoc.version}</version>
        </dependency>

        <!-- JOOQ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq-codegen</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq-kotlin</artifactId>
        </dependency>

        <!-- Flyway -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-postgresql</artifactId>
        </dependency>

        <!-- Postgres -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- Hypersistence from Vlad Mihalcea -->
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>${hypersistence.version}</version>
        </dependency>

        <!-- UUIDv7 -->
        <dependency>
            <groupId>com.fasterxml.uuid</groupId>
            <artifactId>java-uuid-generator</artifactId>
            <version>${uuid-generator.version}</version>
        </dependency>

        <!-- Caffeine caching -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <!-- SQL logging -->
        <dependency>
            <groupId>com.github.gavlyukovskiy</groupId>
            <artifactId>datasource-proxy-spring-boot-starter</artifactId>
            <version>${datasource-decorator.version}</version>
        </dependency>

        <!-- Messaging -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-messaging</artifactId>
        </dependency>

        <!-- Security -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
        </dependency>

        <!-- Firebase -->
        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
            <version>${firebase.version}</version>
        </dependency>

        <!-- Google Cloud -->
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-storage</artifactId>
            <version>2.47.0</version>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>8.0</version>
        </dependency>

        <!-- Locking -->
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>${shedlock.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-template</artifactId>
            <version>${shedlock.version}</version>
        </dependency>

        <!-- Sentry -->
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-spring-boot-starter-jakarta</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-logback</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-jdbc</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-kotlin-extensions</artifactId>
            <version>${sentry.version}</version>
        </dependency>

        <!-- Stripe -->
        <dependency>
            <groupId>com.stripe</groupId>
            <artifactId>stripe-java</artifactId>
            <version>${stripe.version}</version>
        </dependency>

        <!-- Mailing -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars</artifactId>
            <version>${handlebars.version}</version>
        </dependency>

        <!-- Spatial - Java Topology Suite -->
        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>${jts.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-spatial</artifactId>
        </dependency>

        <!-- Observability -->
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-logback-appender</artifactId>
            <version>${loki-logback.version}</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>io.opentelemetry.instrumentation</groupId>-->
        <!--            <artifactId>opentelemetry-spring-boot-starter</artifactId>-->
        <!--            <version>${opentelemetry.instrumentation.version}</version>-->
        <!--        </dependency>-->

        <!-- TESTING -->
        <!-- SpringBoot Test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Test containers-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Kotlin Test suite -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit5</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Mocking -->
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <version>${mockk-jvm.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ninja-squad</groupId>
            <artifactId>springmockk</artifactId> <!-- For @MockkBean -->
            <version>${springmockk.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Kotest -->
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Reflections -->
        <dependency>
            <groupId>io.github.classgraph</groupId>
            <artifactId>classgraph</artifactId>
            <version>${classgraph.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>${project.basedir}/src/main/kotlin</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${maven-versions-plugin.version}</version>
            </plugin>

            <!-- SpringBoot Maven plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- Linting kotlin -->
            <plugin>
                <groupId>com.github.gantsign.maven</groupId>
                <artifactId>ktlint-maven-plugin</artifactId>
                <version>${ktlint-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Kotlin Maven plugin -->
            <!-- Tweak kotlin compilation for JPA - making compiled classes open for proxying by Hibernate -->
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>jpa</plugin>
                    </compilerPlugins>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- Generate class with git info -->
            <!-- Show commit hash in swagger for FE to show that new version was deployed -->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git-commit-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>initialize</phase>
                    </execution>
                </executions>
                <configuration>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>
                        ${project.build.outputDirectory}/git.properties
                    </generateGitPropertiesFilename>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.id.(abbrev|full)$</includeOnlyProperty>
                    </includeOnlyProperties>
                    <commitIdGenerationMode>full</commitIdGenerationMode>
                </configuration>
            </plugin>

            <!-- Runs and reports on test phase -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*IT.java</include>
                    </includes>
                </configuration>
            </plugin>

            <!-- Code coverage analysis -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Enforce build rules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <id>enforce-no-snapshots</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireReleaseDeps>
                                    <message>No Snapshots Allowed!</message>
                                </requireReleaseDeps>
                                <requireJavaVersion>
                                    <version>21</version>
                                    <message>Java 21 is required</message>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>[3.9.9,)</version>
                                    <message>
                                        This project requires Maven 3.9.9 or higher. Please use ./mvnw to ensure the correct version.
                                    </message>
                                </requireMavenVersion>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Util plugin for dependency:tree -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>${maven-dependency-plugin.version}</version>
            </plugin>

            <!-- __JOOQ GENERATION__ Part.1 - start a container -->
            <plugin>
                <groupId>org.codehaus.gmaven</groupId>
                <artifactId>groovy-maven-plugin</artifactId>
                <version>${groovy-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <!-- Start the container in any phase before the actual code
                             generation is required, i.e. at the latest in
                             generate-sources -->
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>execute</goal>
                        </goals>
                        <configuration>
                            <source>
                                import org.testcontainers.containers.PostgreSQLContainer
                                import org.testcontainers.utility.DockerImageName

                                // Define PostGIS image and mark it as a compatible substitute for PostgreSQL
                                def postgisImage = DockerImageName
                                        .parse("postgis/postgis:15-3.5") // keep the same as in tests
                                        .asCompatibleSubstituteFor("postgres")

                                container = new PostgreSQLContainer(postgisImage)
                                        .withUsername("postgres")
                                        .withPassword("postgres")
                                        .withDatabaseName("fundedmind-jooq")

                                container.start()

                                project.properties.setProperty('db.url', container.getJdbcUrl())
                                project.properties.setProperty('db.username', container.getUsername())
                                project.properties.setProperty('db.password', container.getPassword())
                            </source>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.testcontainers</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${testcontainers.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- __JOOQ GENERATION__ Part.2 - run Flyway migrations -->
            <plugin>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <version>${flyway.version}</version>
                <executions>
                    <execution>
                        <!-- Note that we're executing the Flyway plugin in the "generate-sources" phase -->
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <url>${db.url}</url>
                    <user>${db.username}</user>
                    <password>${db.password}</password>
                    <locations>
                        <!-- Note that we need to prefix the db/migration path with filesystem: to prevent Flyway
                      from looking for our migration scripts only on the classpath -->
                        <location>filesystem:src/main/resources/db/migration</location>
                    </locations>
                </configuration>
            </plugin>

            <!-- __JOOQ GENERATION__ Part.3 -  run JOOQ generation-->
            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <version>${jooq.version}</version>
                <executions>
                    <execution>
                        <id>generate-psql</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <jdbc>
                                <driver>org.postgresql.Driver</driver>
                                <url>${db.url}</url>
                                <user>${db.username}</user>
                                <password>${db.password}</password>
                            </jdbc>
                            <generator>
                                <name>org.jooq.codegen.KotlinGenerator</name>
                                <database>
                                    <name>org.jooq.meta.postgres.PostgresDatabase</name>
                                    <includes>.*</includes>
                                    <excludes>flyway_schema_history</excludes>
                                    <inputSchema>public</inputSchema>
                                    <forcedTypes>
                                        <forcedType>
                                            <name>INSTANT</name>
                                            <includeTypes>(?i:TIMESTAMP\ WITH\ TIME\ ZONE)</includeTypes>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.user.appuser.constant.UserRole</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.APP_USER.ROLE</includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.user.student.constant.OnboardingState</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.STUDENT.ONBOARDING_STATE
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.Country</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.COUNTRY
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.GAME_DOCUMENT.STATE
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.GAME_DOCUMENT.TYPE
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.GAME_DOCUMENT.ISSUING_COMPANY
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.GameLevel</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.STUDENT.GAME_LEVEL|
                                                .*\.GAME_DOCUMENT.PREVIOUS_LEVEL|
                                                .*\.GAME_LEVEL_REWARD.GAME_LEVEL|
                                                .*\.GAME_DOCUMENT.REACHED_LEVEL|
                                                .*\.STUDENT_GAME_LEVEL_HISTORY.GAME_LEVEL
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.LevelVisibility</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.STUDENT.LEVEL_VISIBILITY
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.GAME_LEVEL_REWARD.TYPE
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.STUDENT.NETWORKING_VISIBILITY|
                                                .*\.TRADER.NETWORKING_VISIBILITY
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.Color</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.MEETING.COLOR|
                                                .*\.MENTORING_MEETING.COLOR|
                                                .*\.COURSE.COLOR|
                                                .*\.HIGHLIGHT.BUTTON_COLOR|
                                                .*\.COURSE_MODULE.BUTTON_COLOR
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.BadgeColor</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.TRADER.BADGE_COLOR
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.TraderMentoringAvailability</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.TRADER.MENTORING_AVAILABILITY
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.LESSON_ATTACHMENT.TYPE
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.StudentTier</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.STUDENT_TIER
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>kotlin.collections.List&lt;com.cleevio.fundedmind.domain.user.trader.constant.TraderTag&gt;</userType>
                                            <converter>
                                                com.cleevio.fundedmind.application.common.converter.TraderTagListJooqConverter
                                            </converter>
                                            <includeExpression>.*\.TRADER.TAGS</includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>kotlin.collections.Map&lt;String, Any&gt;</userType>
                                            <converter>
                                                com.cleevio.fundedmind.application.common.converter.QuestionnaireJooqConverter
                                            </converter>
                                            <includeExpression>.*\.QUESTIONNAIRE</includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>kotlin.collections.List&lt;com.cleevio.fundedmind.domain.common.constant.StudentTier&gt;</userType>
                                            <converter>
                                                com.cleevio.fundedmind.application.common.converter.StudentTierListJooqConverter
                                            </converter>
                                            <includeExpression>
                                                .*\.MEETING.INVITED_TIERS|
                                                .*\.HIGHLIGHT.VISIBLE_TO_TIERS
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>kotlin.collections.List&lt;com.cleevio.fundedmind.domain.common.constant.StudentTier&gt;</userType>
                                            <converter>
                                                com.cleevio.fundedmind.application.common.converter.StudentTierListJooqConverter
                                            </converter>
                                            <includeExpression>.*\.COURSE.VISIBLE_TO_TIERS</includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.common.constant.CourseCategory</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.COURSE.COURSE_CATEGORY
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.MENTORING_MEETING.MODIFICATION_INITIATOR
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>com.cleevio.fundedmind.domain.mentoringmeeting.constant.ModificationType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.MENTORING_MEETING.MODIFICATION_TYPE
                                            </includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>kotlin.collections.List&lt;com.cleevio.fundedmind.domain.mentoringinquiry.MentoringInquiryAnswer&gt;</userType>
                                            <converter>
                                                com.cleevio.fundedmind.application.common.converter.MentoringInquiryAnswerListJooqConverter
                                            </converter>
                                            <includeExpression>.*\.MENTORING_INQUIRY.ANSWERS</includeExpression>
                                        </forcedType>

                                        <forcedType>
                                            <userType>kotlin.collections.List&lt;com.cleevio.fundedmind.domain.user.trader.MentoringKeypoint&gt;</userType>
                                            <converter>
                                                com.cleevio.fundedmind.application.common.converter.MentoringKeypointListJooqConverter
                                            </converter>
                                            <includeExpression>.*\.TRADER.MENTORING_KEYPOINTS</includeExpression>
                                        </forcedType>

                                    </forcedTypes>
                                </database>
                                <generate>
                                    <defaultCatalog>false</defaultCatalog>
                                    <defaultSchema>false</defaultSchema>
                                    <!-- Implicit join paths use name of the foreign key instead of table name -->
                                    <implicitJoinPathsUseTableNameForUnambiguousFKs>false
                                    </implicitJoinPathsUseTableNameForUnambiguousFKs>

                                    <!-- Disable generation of implicit join paths for To-Many because for this type of relationship we use multiset anyway -->
                                    <implicitJoinPathsToMany>false</implicitJoinPathsToMany>

                                    <!-- Disable generation of stored procedures and functions (such as 'unaccent')-->
                                    <routines>false</routines>

                                    <!-- Disable Java records for data manipulation (e.g. update) -->
                                    <records>false</records>

                                    <!-- use Java 8 java.time types (e.g., LocalDate, LocalTime)
                                   for date/time columns instead of legacy java.sql types -->
                                    <javaTimeTypes>true</javaTimeTypes>

                                    <!-- Don't generate deprecation warnings for unknown types -->
                                    <deprecationOnUnknownTypes>false</deprecationOnUnknownTypes>
                            </generate>
                                <target>
                                    <packageName>com.cleevio.fundedmind.jooq</packageName>
                                    <directory>target/generated-sources/jooq</directory>
                                </target>
                            </generator>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
